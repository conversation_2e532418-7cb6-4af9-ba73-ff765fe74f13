/**
 * 统一API服务
 * 支持uniCloud和SpringBoot两种后端自动切换
 */

import backendConfig from '../components/backend-config.js'

class ApiService {
  constructor() {
    this.backendConfig = backendConfig
    this.init()
  }
  
  /**
   * 初始化API服务
   */
  init() {
    this.backendConfig.init()
    console.log('🚀 API服务初始化完成')
  }
  
  /**
   * 解析视频链接
   * @param {string} url - 视频链接
   * @returns {Promise} 解析结果
   */
  async parseVideo(url) {
    console.log('🎬 开始解析视频:', url)
    
    try {
      if (this.backendConfig.isUniCloud()) {
        return await this._parseVideoUniCloud(url)
      } else if (this.backendConfig.isSpringBoot()) {
        return await this._parseVideoSpringBoot(url)
      } else {
        throw new Error('未知的后端类型')
      }
    } catch (error) {
      console.error('❌ 视频解析失败:', error)
      throw error
    }
  }
  
  /**
   * 使用uniCloud解析视频
   */
  async _parseVideoUniCloud(url) {
    console.log('☁️ 使用uniCloud解析视频')
    
    const config = this.backendConfig.getCurrentBackendConfig()
    
    try {
      const result = await uniCloud.callFunction({
        name: config.config.functions.parser,
        data: {
          link: url
        }
      })
      
      if (result.result) {
        console.log('✅ uniCloud解析成功')
        return this._normalizeUniCloudResponse(result.result)
      } else {
        throw new Error('uniCloud调用失败')
      }
    } catch (error) {
      console.error('❌ uniCloud解析失败:', error)
      throw new Error(`uniCloud解析失败: ${error.message}`)
    }
  }
  
  /**
   * 使用SpringBoot解析视频
   */
  async _parseVideoSpringBoot(url) {
    console.log('🌱 使用SpringBoot解析视频')
    
    const config = this.backendConfig.getCurrentBackendConfig()
    
    try {
      // 获取API Key
      const apiKey = await this.backendConfig.getSpringBootApiKey()
      
      // 发送解析请求
      const response = await uni.request({
        url: config.baseUrl + config.config.endpoints.parse,
        method: 'POST',
        header: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey
        },
        data: {
          url: url
        },
        timeout: config.timeout
      })
      
      const [error, res] = response
      if (error) {
        throw new Error(`网络请求失败: ${error.message}`)
      }
      
      if (res.statusCode === 200 && res.data.success) {
        console.log('✅ SpringBoot解析成功')
        return this._normalizeSpringBootResponse(res.data)
      } else if (res.statusCode === 403) {
        // API Key可能过期，清除缓存并重试
        console.log('🔑 API Key可能过期，清除缓存并重试')
        this.backendConfig.clearSpringBootApiKey()
        throw new Error('API Key无效，请重试')
      } else {
        throw new Error(res.data?.message || `HTTP ${res.statusCode}`)
      }
    } catch (error) {
      console.error('❌ SpringBoot解析失败:', error)
      throw new Error(`SpringBoot解析失败: ${error.message}`)
    }
  }
  
  /**
   * 标准化uniCloud响应格式
   */
  _normalizeUniCloudResponse(response) {
    // uniCloud返回的数据已经是标准格式，直接返回
    return response
  }
  
  /**
   * 标准化SpringBoot响应格式
   */
  _normalizeSpringBootResponse(response) {
    // SpringBoot返回的数据已经按照对接文档标准化，直接返回
    return response.data
  }
  
  /**
   * 检测平台类型
   * @param {string} url - 视频链接
   * @returns {Promise<string>} 平台代码
   */
  async detectPlatform(url) {
    console.log('🔍 检测平台类型:', url)
    
    try {
      if (this.backendConfig.isUniCloud()) {
        // uniCloud版本：使用本地检测逻辑
        return this._detectPlatformLocal(url)
      } else if (this.backendConfig.isSpringBoot()) {
        return await this._detectPlatformSpringBoot(url)
      } else {
        throw new Error('未知的后端类型')
      }
    } catch (error) {
      console.error('❌ 平台检测失败:', error)
      // 降级到本地检测
      return this._detectPlatformLocal(url)
    }
  }
  
  /**
   * 本地平台检测逻辑
   */
  _detectPlatformLocal(url) {
    const platformPatterns = {
      'douyin': [/douyin\.com/, /iesdouyin\.com/, /dy\.tt/],
      'kuaishou': [/kuaishou\.com/, /chenzhongtech\.com/, /ks\.cn/],
      'xiaohongshu': [/xiaohongshu\.com/, /xhslink\.com/],
      'bilibili': [/bilibili\.com/, /b23\.tv/],
      'weibo': [/weibo\.com/, /weibo\.cn/],
      'weishi': [/weishi\.qq\.com/],
      'pipix': [/pipix\.com/, /pipigx\.com/],
      'qishui': [/qishui\.douyin\.com/],
      'zuiyou': [/xiaochuankeji\.cn/],
      'haokan': [/haokan\.baidu\.com/]
    }
    
    for (const [platform, patterns] of Object.entries(platformPatterns)) {
      if (patterns.some(pattern => pattern.test(url))) {
        console.log(`✅ 检测到平台: ${platform}`)
        return platform
      }
    }
    
    console.log('❓ 未知平台')
    return 'unknown'
  }
  
  /**
   * 使用SpringBoot检测平台
   */
  async _detectPlatformSpringBoot(url) {
    const config = this.backendConfig.getCurrentBackendConfig()
    const apiKey = await this.backendConfig.getSpringBootApiKey()
    
    const response = await uni.request({
      url: config.baseUrl + config.config.endpoints.detect + '?url=' + encodeURIComponent(url),
      method: 'GET',
      header: {
        'X-API-Key': apiKey
      },
      timeout: config.timeout
    })
    
    const [error, res] = response
    if (error) {
      throw new Error(`网络请求失败: ${error.message}`)
    }
    
    if (res.statusCode === 200) {
      return res.data  // SpringBoot直接返回平台代码字符串
    } else {
      throw new Error(`HTTP ${res.statusCode}`)
    }
  }
  
  /**
   * 获取支持的平台列表
   * @returns {Promise<Array>} 平台列表
   */
  async getSupportedPlatforms() {
    console.log('📋 获取支持的平台列表')
    
    try {
      if (this.backendConfig.isUniCloud()) {
        // uniCloud版本：返回本地配置的平台列表
        return ['douyin', 'kuaishou', 'xiaohongshu', 'bilibili', 'weibo', 'weishi', 'pipix', 'qishui', 'zuiyou', 'haokan']
      } else if (this.backendConfig.isSpringBoot()) {
        return await this._getSupportedPlatformsSpringBoot()
      } else {
        throw new Error('未知的后端类型')
      }
    } catch (error) {
      console.error('❌ 获取平台列表失败:', error)
      // 降级到本地列表
      return ['douyin', 'kuaishou', 'xiaohongshu', 'bilibili', 'weibo', 'weishi', 'pipix', 'qishui', 'zuiyou', 'haokan']
    }
  }
  
  /**
   * 使用SpringBoot获取平台列表
   */
  async _getSupportedPlatformsSpringBoot() {
    const config = this.backendConfig.getCurrentBackendConfig()
    
    const response = await uni.request({
      url: config.baseUrl + config.config.endpoints.platforms,
      method: 'GET',
      timeout: config.timeout
    })
    
    const [error, res] = response
    if (error) {
      throw new Error(`网络请求失败: ${error.message}`)
    }
    
    if (res.statusCode === 200) {
      return res.data  // SpringBoot返回平台数组
    } else {
      throw new Error(`HTTP ${res.statusCode}`)
    }
  }
  
  /**
   * 健康检查
   * @returns {Promise<boolean>} 服务是否正常
   */
  async healthCheck() {
    console.log('🏥 执行健康检查')
    
    try {
      if (this.backendConfig.isUniCloud()) {
        // uniCloud版本：检查云函数是否可用
        return await this._healthCheckUniCloud()
      } else if (this.backendConfig.isSpringBoot()) {
        return await this._healthCheckSpringBoot()
      } else {
        return false
      }
    } catch (error) {
      console.error('❌ 健康检查失败:', error)
      return false
    }
  }
  
  /**
   * uniCloud健康检查
   */
  async _healthCheckUniCloud() {
    try {
      const result = await uniCloud.callFunction({
        name: 'unified-parser',
        data: { healthCheck: true }
      })
      return !!result.result
    } catch (error) {
      return false
    }
  }
  
  /**
   * SpringBoot健康检查
   */
  async _healthCheckSpringBoot() {
    const config = this.backendConfig.getCurrentBackendConfig()
    
    try {
      const response = await uni.request({
        url: config.baseUrl + config.config.endpoints.health,
        method: 'GET',
        timeout: 5000  // 健康检查使用较短超时
      })
      
      const [error, res] = response
      return !error && res.statusCode === 200 && res.data === 'OK'
    } catch (error) {
      return false
    }
  }
  
  /**
   * 获取当前后端信息
   */
  getCurrentBackendInfo() {
    const config = this.backendConfig.getCurrentBackendConfig()
    return {
      type: config.type,
      name: this.backendConfig.getCurrentBackendName(),
      config: config
    }
  }

  /**
   * 切换后端类型
   */
  switchBackend(backendType) {
    return this.backendConfig.switchBackend(backendType)
  }

  /**
   * 切换SpringBoot环境
   */
  switchSpringBootEnv(env) {
    return this.backendConfig.switchSpringBootEnv(env)
  }
}

// 创建单例实例
const apiService = new ApiService()

export default apiService
