# SpringBoot版本短视频解析器开发文档

## 项目概述

### 背景
当前项目使用uniCloud云函数实现短视频解析，但遇到以下限制：
- 无法执行JavaScript获取动态数据
- HTTP客户端功能受限
- 调试和扩展困难
- 无法使用无头浏览器等高级功能

### 目标
开发SpringBoot版本的解析器，提供更强大的解析能力，支持现代SPA架构的视频平台。

## 技术架构

### 整体架构
```
前端(uniApp) → uniCloud(代理层) → SpringBoot解析服务 → 各平台API
```

### 核心组件
1. **HTTP客户端模块** - 处理网络请求和重定向
2. **JavaScript执行引擎** - 执行页面JS获取动态数据
3. **HTML解析模块** - 解析页面结构和数据
4. **平台解析器** - 各平台特定的解析逻辑
5. **无头浏览器模块** - 处理复杂的SPA页面
6. **缓存模块** - 提升解析性能

## 技术栈选择

### 核心依赖
```xml
<dependencies>
    <!-- SpringBoot基础 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    
    <!-- HTTP客户端 -->
    <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp</artifactId>
        <version>4.12.0</version>
    </dependency>
    
    <!-- HTML解析 -->
    <dependency>
        <groupId>org.jsoup</groupId>
        <artifactId>jsoup</artifactId>
        <version>1.17.2</version>
    </dependency>
    
    <!-- JavaScript执行引擎 -->
    <dependency>
        <groupId>org.graalvm.js</groupId>
        <artifactId>js</artifactId>
        <version>23.0.2</version>
    </dependency>
    <dependency>
        <groupId>org.graalvm.js</groupId>
        <artifactId>js-scriptengine</artifactId>
        <version>23.0.2</version>
    </dependency>
    
    <!-- 无头浏览器 -->
    <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-chrome-driver</artifactId>
        <version>4.15.0</version>
    </dependency>
    
    <!-- JSON处理 -->
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
    </dependency>
    
    <!-- 缓存 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-cache</artifactId>
    </dependency>
    <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>caffeine</artifactId>
    </dependency>
    
    <!-- 日志 -->
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-logging</artifactId>
    </dependency>
</dependencies>
```

## 项目结构

```
src/main/java/com/markeraser/parser/
├── ParserApplication.java                 # 启动类
├── config/                               # 配置类
│   ├── HttpClientConfig.java            # HTTP客户端配置
│   ├── WebDriverConfig.java             # 无头浏览器配置
│   ├── CacheConfig.java                 # 缓存配置
│   └── JsEngineConfig.java              # JS引擎配置
├── controller/                          # 控制器
│   └── VideoParserController.java       # 解析API接口
├── service/                             # 服务层
│   ├── VideoParserService.java          # 解析服务接口
│   ├── impl/
│   │   └── VideoParserServiceImpl.java  # 解析服务实现
│   ├── platform/                       # 平台解析器 (10个平台)
│   │   ├── PlatformParser.java          # 解析器接口
│   │   ├── DouyinParser.java            # 1. 抖音解析器
│   │   ├── KuaishouParser.java          # 2. 快手解析器
│   │   ├── XiaohongshuParser.java       # 3. 小红书解析器
│   │   ├── BilibiliParser.java          # 4. B站解析器
│   │   ├── WeiboParser.java             # 5. 微博解析器
│   │   ├── WeishiParser.java            # 6. 微视解析器 ⚠️ (SPA架构)
│   │   ├── PipixParser.java             # 7. 皮皮虾解析器
│   │   ├── QishuiMusicParser.java       # 8. 汽水音乐解析器
│   │   ├── ZuiyouParser.java            # 9. 最右解析器
│   │   └── HaokanParser.java            # 10. 好看视频解析器
│   └── util/                           # 工具服务
│       ├── HttpClientService.java       # HTTP客户端服务
│       ├── JsExecutorService.java       # JS执行服务
│       ├── WebDriverService.java        # 无头浏览器服务
│       └── HtmlParserService.java       # HTML解析服务
├── model/                              # 数据模型
│   ├── VideoInfo.java                  # 视频信息模型
│   ├── ParseRequest.java               # 解析请求模型
│   ├── ParseResponse.java              # 解析响应模型
│   └── PlatformType.java               # 平台类型枚举 (10个平台)
└── exception/                          # 异常处理
    ├── ParseException.java             # 解析异常
    └── GlobalExceptionHandler.java     # 全局异常处理
```

## 核心接口设计

### 1. 解析器接口
```java
public interface PlatformParser {
    /**
     * 判断是否支持该URL
     */
    boolean supports(String url);
    
    /**
     * 获取平台类型
     */
    PlatformType getPlatformType();
    
    /**
     * 解析视频信息
     */
    VideoInfo parse(String url) throws ParseException;
    
    /**
     * 清理URL（去除跟踪参数等）
     */
    String cleanUrl(String url);
}
```

### 2. 视频信息模型
```java
@Data
public class VideoInfo {
    private String title;           // 视频标题
    private String author;          // 作者昵称
    private String authorId;        // 作者ID
    private String avatar;          // 作者头像
    private String cover;           // 视频封面
    private String videoUrl;        // 视频播放链接
    private List<String> videoUrls; // 多清晰度链接
    private String musicUrl;        // 背景音乐链接
    private String musicTitle;      // 音乐标题
    private Long duration;          // 视频时长(秒)
    private Long playCount;         // 播放次数
    private Long likeCount;         // 点赞数
    private Long commentCount;      // 评论数
    private Long shareCount;        // 分享数
    private Long createTime;        // 发布时间
    private String description;     // 视频描述
    private PlatformType platform;  // 平台类型
    private String originalUrl;     // 原始链接
}
```

### 3. 平台类型枚举
```java
public enum PlatformType {
    DOUYIN("douyin", "抖音", "视频和图集解析"),
    KUAISHOU("kuaishou", "快手", "视频解析"),
    XIAOHONGSHU("xiaohongshu", "小红书", "视频和图集解析"),
    BILIBILI("bilibili", "B站", "视频解析"),
    WEIBO("weibo", "微博", "视频和图集解析"),
    WEISHI("weishi", "微视", "视频解析"),
    PIPIX("pipix", "皮皮虾", "视频解析"),
    QISHUI_MUSIC("qishui", "汽水音乐", "音乐解析"),
    ZUIYOU("zuiyou", "最右", "视频和图集解析，支持Live Photo"),
    HAOKAN("haokan", "好看视频", "视频解析，支持多清晰度"),
    UNKNOWN("unknown", "未知平台", "不支持");

    private final String code;
    private final String name;
    private final String description;

    PlatformType(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }

    public String getCode() { return code; }
    public String getName() { return name; }
    public String getDescription() { return description; }

    public static PlatformType fromCode(String code) {
        for (PlatformType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return UNKNOWN;
    }
}
```

### 4. API接口
```java
@RestController
@RequestMapping("/api/v1/parser")
public class VideoParserController {
    
    @PostMapping("/parse")
    public ParseResponse parseVideo(@RequestBody ParseRequest request) {
        // 解析视频逻辑
    }
    
    @GetMapping("/parse")
    public ParseResponse parseVideoByUrl(@RequestParam String url) {
        // 通过URL参数解析
    }
    
    @GetMapping("/platforms")
    public List<PlatformType> getSupportedPlatforms() {
        // 获取支持的平台列表
    }
}
```

## 各平台解析实现策略

### 1. 抖音解析器 (DouyinParser)
```java
@Component
public class DouyinParser implements PlatformParser {
    
    @Override
    public VideoInfo parse(String url) throws ParseException {
        // 1. 清理URL，提取视频ID
        String cleanUrl = cleanUrl(url);
        String videoId = extractVideoId(cleanUrl);
        
        // 2. 构造API请求
        String apiUrl = buildApiUrl(videoId);
        
        // 3. 发送请求获取数据
        String response = httpClientService.get(apiUrl, buildHeaders());
        
        // 4. 解析JSON响应
        return parseJsonResponse(response);
    }
    
    private Map<String, String> buildHeaders() {
        return Map.of(
            "User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15",
            "Referer", "https://www.douyin.com/",
            "Accept", "application/json, text/plain, */*"
        );
    }
}
```

### 2. 微视解析器 (WeishiParser) - 重点
```java
@Component
public class WeishiParser implements PlatformParser {
    
    @Override
    public VideoInfo parse(String url) throws ParseException {
        try {
            // 方案1: 尝试传统HTML解析
            VideoInfo result = parseFromHtml(url);
            if (result != null) {
                return result;
            }
            
            // 方案2: 使用JavaScript执行
            result = parseWithJsEngine(url);
            if (result != null) {
                return result;
            }
            
            // 方案3: 使用无头浏览器
            return parseWithWebDriver(url);
            
        } catch (Exception e) {
            throw new ParseException("微视解析失败: " + e.getMessage(), e);
        }
    }
    
    private VideoInfo parseFromHtml(String url) {
        // 获取HTML并尝试提取JSON数据
        String html = httpClientService.get(url, buildMobileHeaders());
        
        // 提取 window.Vise.initState
        Pattern pattern = Pattern.compile("window\\.Vise\\.initState\\s*=\\s*({[\\s\\S]*?});");
        Matcher matcher = pattern.matcher(html);
        
        if (matcher.find()) {
            String jsonStr = matcher.group(1);
            JsonNode jsonData = objectMapper.readTree(jsonStr);
            
            // 检查是否有视频数据
            JsonNode feedsList = jsonData.get("feedsList");
            if (feedsList != null && feedsList.isArray() && feedsList.size() > 0) {
                return parseVideoFromJson(feedsList.get(0));
            }
        }
        
        return null;
    }
    
    private VideoInfo parseWithJsEngine(String url) {
        // 获取页面HTML
        String html = httpClientService.get(url, buildMobileHeaders());
        
        // 提取JavaScript代码
        List<String> jsUrls = extractJavaScriptUrls(html);
        
        for (String jsUrl : jsUrls) {
            try {
                String jsCode = httpClientService.get(jsUrl, buildHeaders());
                
                // 在JS引擎中执行代码
                Object result = jsExecutorService.executeScript(jsCode, url);
                
                if (result != null) {
                    return parseJsResult(result);
                }
            } catch (Exception e) {
                log.warn("JS执行失败: {}", e.getMessage());
            }
        }
        
        return null;
    }
    
    private VideoInfo parseWithWebDriver(String url) {
        return webDriverService.executeWithBrowser(driver -> {
            driver.get(url);
            
            // 等待数据加载
            WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));
            wait.until(d -> 
                ((JavascriptExecutor) d).executeScript(
                    "return window.Vise && window.Vise.initState && window.Vise.initState.feedsList.length > 0"
                )
            );
            
            // 获取动态数据
            String jsonData = (String) ((JavascriptExecutor) driver).executeScript(
                "return JSON.stringify(window.Vise.initState)"
            );
            
            return parseJsonData(jsonData);
        });
    }
}
```

### 3. 小红书解析器 (XiaohongshuParser)
```java
@Component
public class XiaohongshuParser implements PlatformParser {
    
    @Override
    public VideoInfo parse(String url) throws ParseException {
        // 小红书需要特殊的请求头和Cookie处理
        Map<String, String> headers = Map.of(
            "User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15",
            "Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language", "zh-CN,zh;q=0.9",
            "Referer", "https://www.xiaohongshu.com/"
        );
        
        String html = httpClientService.get(url, headers);
        return parseXhsHtml(html);
    }
}
```

## 核心服务实现

### 1. HTTP客户端服务
```java
@Service
public class HttpClientService {
    
    private final OkHttpClient client;
    
    public String get(String url, Map<String, String> headers) {
        Request.Builder builder = new Request.Builder().url(url);
        
        // 添加请求头
        headers.forEach(builder::addHeader);
        
        try (Response response = client.newCall(builder.build()).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("请求失败: " + response.code());
            }
            return response.body().string();
        } catch (IOException e) {
            throw new RuntimeException("HTTP请求异常", e);
        }
    }
    
    public String post(String url, String json, Map<String, String> headers) {
        RequestBody body = RequestBody.create(json, MediaType.get("application/json"));
        Request.Builder builder = new Request.Builder()
            .url(url)
            .post(body);
            
        headers.forEach(builder::addHeader);
        
        try (Response response = client.newCall(builder.build()).execute()) {
            return response.body().string();
        } catch (IOException e) {
            throw new RuntimeException("HTTP POST请求异常", e);
        }
    }
}
```

### 2. JavaScript执行服务
```java
@Service
public class JsExecutorService {
    
    private final ScriptEngine engine;
    
    public Object executeScript(String jsCode, String url) {
        try {
            // 设置上下文
            engine.put("window", new HashMap<>());
            engine.put("document", new HashMap<>());
            engine.put("location", Map.of("href", url));
            
            // 执行JavaScript
            return engine.eval(jsCode);
        } catch (ScriptException e) {
            log.error("JavaScript执行失败", e);
            return null;
        }
    }
}
```

### 3. 无头浏览器服务
```java
@Service
public class WebDriverService {
    
    public <T> T executeWithBrowser(Function<WebDriver, T> action) {
        ChromeOptions options = new ChromeOptions();
        options.addArguments("--headless");
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");
        
        WebDriver driver = new ChromeDriver(options);
        
        try {
            return action.apply(driver);
        } finally {
            driver.quit();
        }
    }
}
```

## API安全鉴权设计

### 鉴权方案：API密钥 + 签名验证 + 时间戳

考虑到微信小程序通过域名白名单调用的特点，采用以下安全方案：

#### 1. 安全配置
```yaml
# application.yml
app:
  security:
    api-key: "${API_SECRET_KEY:your-secret-api-key-here}"
    signature-secret: "${SIGNATURE_SECRET:your-signature-secret-here}"
    request-timeout: 300000  # 5分钟请求有效期
    rate-limit:
      requests-per-minute: 100
      requests-per-hour: 1000
```

#### 2. 安全控制器
```java
@RestController
@RequestMapping("/api/v1/parser")
public class VideoParserController {

    @Autowired
    private SecurityService securityService;

    @Autowired
    private VideoParserService videoParserService;

    @PostMapping("/parse")
    public ParseResponse parseVideo(
        @RequestBody ParseRequest request,
        @RequestHeader("X-API-Key") String apiKey,
        @RequestHeader("X-Timestamp") String timestamp,
        @RequestHeader("X-Signature") String signature,
        @RequestHeader("X-Client-Type") String clientType
    ) {
        // 安全验证
        SecurityValidationResult validation = securityService.validateRequest(
            request, apiKey, timestamp, signature, clientType
        );

        if (!validation.isValid()) {
            throw new UnauthorizedException(validation.getErrorMessage());
        }

        // 执行解析
        VideoInfo videoInfo = videoParserService.parseVideo(request.getUrl());
        return ParseResponse.success(videoInfo);
    }
}
```

#### 3. 安全服务实现
```java
@Service
public class SecurityService {

    @Value("${app.security.api-key}")
    private String validApiKey;

    @Value("${app.security.signature-secret}")
    private String signatureSecret;

    @Value("${app.security.request-timeout}")
    private long requestTimeout;

    public SecurityValidationResult validateRequest(
        ParseRequest request,
        String apiKey,
        String timestamp,
        String signature,
        String clientType
    ) {

        // 1. 验证API密钥
        if (!validApiKey.equals(apiKey)) {
            return SecurityValidationResult.invalid("Invalid API key");
        }

        // 2. 验证客户端类型
        if (!"unicloud-miniprogram".equals(clientType)) {
            return SecurityValidationResult.invalid("Invalid client type");
        }

        // 3. 验证时间戳（防重放攻击）
        try {
            long requestTime = Long.parseLong(timestamp);
            long currentTime = System.currentTimeMillis();
            if (Math.abs(currentTime - requestTime) > requestTimeout) {
                return SecurityValidationResult.invalid("Request expired");
            }
        } catch (NumberFormatException e) {
            return SecurityValidationResult.invalid("Invalid timestamp format");
        }

        // 4. 验证签名
        String expectedSignature = generateSignature(request.getUrl(), apiKey, timestamp);
        if (!signature.equals(expectedSignature)) {
            return SecurityValidationResult.invalid("Invalid signature");
        }

        return SecurityValidationResult.valid();
    }

    private String generateSignature(String url, String apiKey, String timestamp) {
        // 签名算法: SHA256(url + apiKey + timestamp + signatureSecret)
        String data = url + apiKey + timestamp + signatureSecret;
        return DigestUtils.sha256Hex(data);
    }
}

@Data
public class SecurityValidationResult {
    private boolean valid;
    private String errorMessage;

    public static SecurityValidationResult valid() {
        SecurityValidationResult result = new SecurityValidationResult();
        result.setValid(true);
        return result;
    }

    public static SecurityValidationResult invalid(String errorMessage) {
        SecurityValidationResult result = new SecurityValidationResult();
        result.setValid(false);
        result.setErrorMessage(errorMessage);
        return result;
    }
}
```

#### 4. 限流配置
```java
@Configuration
public class RateLimitConfig {

    @Bean
    public RedisTemplate<String, String> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setDefaultSerializer(new StringRedisSerializer());
        return template;
    }
}

@Component
public class RateLimitInterceptor implements HandlerInterceptor {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Value("${app.security.rate-limit.requests-per-minute}")
    private int requestsPerMinute;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String apiKey = request.getHeader("X-API-Key");
        if (apiKey == null) return true;

        String key = "rate_limit:" + apiKey + ":" + (System.currentTimeMillis() / 60000);
        String count = redisTemplate.opsForValue().get(key);

        if (count == null) {
            redisTemplate.opsForValue().set(key, "1", Duration.ofMinutes(1));
            return true;
        }

        int currentCount = Integer.parseInt(count);
        if (currentCount >= requestsPerMinute) {
            response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
            return false;
        }

        redisTemplate.opsForValue().increment(key);
        return true;
    }
}
```

## 与uniCloud对接

### 1. uniCloud代理函数（带安全验证）
```javascript
// uniCloud/cloudfunctions/video-parser-proxy/index.js
const crypto = require('crypto');

exports.main = async (event, context) => {
    const { url } = event;

    // 安全配置（存储在云函数环境变量中）
    const API_KEY = process.env.SPRINGBOOT_API_KEY || 'your-secret-api-key';
    const SIGNATURE_SECRET = process.env.SIGNATURE_SECRET || 'your-signature-secret';
    const SPRINGBOOT_API_URL = process.env.SPRINGBOOT_API_URL || 'https://your-domain.com';

    try {
        // 生成时间戳
        const timestamp = Date.now().toString();

        // 生成签名
        const signatureData = url + API_KEY + timestamp + SIGNATURE_SECRET;
        const signature = crypto.createHash('sha256').update(signatureData).digest('hex');

        // 调用SpringBoot解析服务
        const response = await uniCloud.httpclient.request(SPRINGBOOT_API_URL + '/api/v1/parser/parse', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-API-Key': API_KEY,
                'X-Timestamp': timestamp,
                'X-Signature': signature,
                'X-Client-Type': 'unicloud-miniprogram'
            },
            data: JSON.stringify({ url })
        });

        return {
            success: true,
            data: response.data
        };
    } catch (error) {
        console.error('SpringBoot API调用失败:', error);
        return {
            success: false,
            message: error.message || '解析服务暂时不可用'
        };
    }
};
```

### 2. 环境变量配置
```bash
# uniCloud云函数环境变量配置
SPRINGBOOT_API_KEY=your-secret-api-key-here
SIGNATURE_SECRET=your-signature-secret-here
SPRINGBOOT_API_URL=https://your-springboot-domain.com
```

### 3. 前端调用（无需修改）
```javascript
// 前端调用保持完全不变
const result = await uniCloud.callFunction({
    name: 'video-parser-proxy',
    data: { url: videoUrl }
});

if (result.result.success) {
    // 处理解析成功的数据
    const videoInfo = result.result.data;
} else {
    // 处理解析失败
    console.error('解析失败:', result.result.message);
}
```

### 4. 微信小程序域名配置
在微信小程序后台配置request合法域名：
```
https://your-springboot-domain.com
```

### 5. 安全特性总结
- ✅ **API密钥验证** - 防止未授权访问
- ✅ **数字签名** - 防止请求被篡改
- ✅ **时间戳验证** - 防止重放攻击（5分钟有效期）
- ✅ **客户端类型验证** - 确保只有指定客户端可以访问
- ✅ **请求限流** - 防止恶意刷量（每分钟100次，每小时1000次）
- ✅ **错误处理** - 详细的错误信息和日志记录

## 部署和配置

### 1. 应用配置
```yaml
# application.yml
server:
  port: 8080

spring:
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=1h

parser:
  webdriver:
    chrome-driver-path: /usr/bin/chromedriver
    headless: true
  http:
    timeout: 30s
    max-connections: 100
  cache:
    enabled: true
    ttl: 3600
```

### 2. Docker部署
```dockerfile
FROM openjdk:17-jdk-slim

# 安装Chrome
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    && wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

COPY target/video-parser-*.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 性能优化建议

1. **连接池配置** - 合理配置HTTP连接池大小
2. **缓存策略** - 对解析结果进行缓存，避免重复解析
3. **异步处理** - 使用异步方式处理耗时的解析操作
4. **资源管理** - 及时释放WebDriver资源
5. **限流控制** - 避免对目标平台造成过大压力

## 监控和日志

1. **解析成功率监控** - 统计各平台解析成功率
2. **性能监控** - 监控解析耗时和资源使用
3. **错误日志** - 详细记录解析失败的原因
4. **访问日志** - 记录API调用情况

## 当前uniCloud解析器实现分析

### 已实现的平台解析器

#### 1. 统一解析器 (unified-parser)
**位置**: `uniCloud-aliyun/cloudfunctions/unified-parser/index.js`

**核心逻辑**:
```javascript
// 平台识别和路由
function detectPlatform(url) {
    if (url.includes('douyin.com') || url.includes('iesdouyin.com')) return 'douyin';
    if (url.includes('kuaishou.com') || url.includes('chenzhongtech.com')) return 'kuaishou';
    if (url.includes('xiaohongshu.com') || url.includes('xhslink.com')) return 'xiaohongshu';
    if (url.includes('bilibili.com') || url.includes('b23.tv')) return 'bilibili';
    if (url.includes('weibo.com') || url.includes('weibo.cn')) return 'weibo';
    if (url.includes('weishi.qq.com')) return 'weishi';
    return 'unknown';
}

// 调用对应平台解析器
const result = await uniCloud.callFunction({
    name: `${platform}-parser`,
    data: { link: cleanedUrl }
});
```

**SpringBoot对应实现**:
```java
@Service
public class VideoParserServiceImpl implements VideoParserService {

    private final Map<PlatformType, PlatformParser> parsers;

    @Override
    public VideoInfo parseVideo(String url) {
        PlatformType platform = detectPlatform(url);
        PlatformParser parser = parsers.get(platform);

        if (parser == null) {
            throw new ParseException("不支持的平台: " + platform);
        }

        return parser.parse(url);
    }

    private PlatformType detectPlatform(String url) {
        if (url.contains("douyin.com") || url.contains("iesdouyin.com")) {
            return PlatformType.DOUYIN;
        }
        if (url.contains("kuaishou.com") || url.contains("chenzhongtech.com")) {
            return PlatformType.KUAISHOU;
        }
        if (url.contains("xiaohongshu.com") || url.contains("xhslink.com")) {
            return PlatformType.XIAOHONGSHU;
        }
        if (url.contains("bilibili.com") || url.contains("b23.tv")) {
            return PlatformType.BILIBILI;
        }
        if (url.contains("weibo.com") || url.contains("weibo.cn")) {
            return PlatformType.WEIBO;
        }
        if (url.contains("weishi.qq.com")) {
            return PlatformType.WEISHI;
        }
        if (url.contains("pipix.com") || url.contains("h5.pipix.com")) {
            return PlatformType.PIPIX;
        }
        if (url.contains("qishui.douyin.com")) {
            return PlatformType.QISHUI_MUSIC;
        }
        if (url.contains("share.xiaochuankeji.cn")) {
            return PlatformType.ZUIYOU;
        }
        if (url.contains("haokan.baidu.com")) {
            return PlatformType.HAOKAN;
        }
        return PlatformType.UNKNOWN;
        if (url.contains("haokan.baidu.com")) {
            return PlatformType.HAOKAN;
        }
        return PlatformType.UNKNOWN;
    }
}
```

#### 2. 抖音解析器 (douyin-parser)
**位置**: `uniCloud-aliyun/cloudfunctions/douyin-parser/index.js`

**核心实现**:
```javascript
// URL清理和重定向处理
async function getDouyinRealUrl(url) {
    const response = await uniCloud.httpclient.request(url, {
        method: 'GET',
        followRedirect: false,
        headers: {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)'
        }
    });

    if (response.status === 302 && response.headers.location) {
        return response.headers.location;
    }
    return url;
}

// 视频ID提取
function extractVideoId(url) {
    const patterns = [
        /video\/(\d+)/,
        /\/(\d+)$/,
        /aweme_id=(\d+)/
    ];

    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match) return match[1];
    }
    return null;
}

// API调用
async function fetchDouyinVideoInfo(videoId) {
    const apiUrl = `https://www.iesdouyin.com/web/api/v2/aweme/iteminfo/?item_ids=${videoId}`;
    const response = await uniCloud.httpclient.request(apiUrl, {
        headers: {
            'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)',
            'Referer': 'https://www.douyin.com/'
        }
    });

    return JSON.parse(response.data);
}
```

**SpringBoot对应实现**:
```java
@Component
public class DouyinParser implements PlatformParser {

    @Override
    public VideoInfo parse(String url) throws ParseException {
        try {
            // 1. 获取真实URL
            String realUrl = getDouyinRealUrl(url);

            // 2. 提取视频ID
            String videoId = extractVideoId(realUrl);
            if (videoId == null) {
                throw new ParseException("无法提取视频ID");
            }

            // 3. 调用API获取视频信息
            String apiUrl = String.format(
                "https://www.iesdouyin.com/web/api/v2/aweme/iteminfo/?item_ids=%s",
                videoId
            );

            Map<String, String> headers = Map.of(
                "User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)",
                "Referer", "https://www.douyin.com/"
            );

            String response = httpClientService.get(apiUrl, headers);

            // 4. 解析响应
            return parseDouyinResponse(response);

        } catch (Exception e) {
            throw new ParseException("抖音解析失败: " + e.getMessage(), e);
        }
    }

    private String getDouyinRealUrl(String url) {
        // 处理重定向逻辑
        try {
            Response response = httpClientService.head(url, false); // 不跟随重定向
            if (response.code() == 302) {
                return response.header("Location");
            }
            return url;
        } catch (Exception e) {
            return url;
        }
    }

    private VideoInfo parseDouyinResponse(String response) {
        try {
            JsonNode root = objectMapper.readTree(response);
            JsonNode itemList = root.path("item_list");

            if (itemList.isArray() && itemList.size() > 0) {
                JsonNode item = itemList.get(0);

                VideoInfo videoInfo = new VideoInfo();
                videoInfo.setTitle(item.path("desc").asText());
                videoInfo.setAuthor(item.path("author").path("nickname").asText());
                videoInfo.setAuthorId(item.path("author").path("unique_id").asText());
                videoInfo.setAvatar(item.path("author").path("avatar_thumb").path("url_list").get(0).asText());
                videoInfo.setCover(item.path("video").path("cover").path("url_list").get(0).asText());

                // 提取视频URL
                JsonNode playAddr = item.path("video").path("play_addr");
                if (playAddr.has("url_list") && playAddr.path("url_list").isArray()) {
                    String videoUrl = playAddr.path("url_list").get(0).asText();
                    videoInfo.setVideoUrl(videoUrl);
                }

                // 统计数据
                JsonNode statistics = item.path("statistics");
                videoInfo.setPlayCount(statistics.path("play_count").asLong());
                videoInfo.setLikeCount(statistics.path("digg_count").asLong());
                videoInfo.setCommentCount(statistics.path("comment_count").asLong());
                videoInfo.setShareCount(statistics.path("share_count").asLong());

                videoInfo.setPlatform(PlatformType.DOUYIN);
                return videoInfo;
            }

            throw new ParseException("抖音API返回数据为空");

        } catch (Exception e) {
            throw new ParseException("解析抖音响应失败: " + e.getMessage(), e);
        }
    }
}
```

#### 3. 快手解析器 (kuaishou-parser)
**位置**: `uniCloud-aliyun/cloudfunctions/kuaishou-parser/index.js`

**核心特点**:
- 需要处理多种URL格式
- 使用特殊的API端点
- 需要构造特定的请求头

**SpringBoot实现要点**:
```java
@Component
public class KuaishouParser implements PlatformParser {

    private static final String API_BASE = "https://www.kuaishou.com/graphql";

    @Override
    public VideoInfo parse(String url) throws ParseException {
        // 快手需要GraphQL查询
        String photoId = extractPhotoId(url);
        String query = buildGraphQLQuery(photoId);

        Map<String, String> headers = Map.of(
            "User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Content-Type", "application/json",
            "Referer", "https://www.kuaishou.com/"
        );

        String response = httpClientService.post(API_BASE, query, headers);
        return parseKuaishouResponse(response);
    }

    private String buildGraphQLQuery(String photoId) {
        return String.format("""
            {
                "query": "query visionVideoDetail($photoId: String, $type: String) {
                    visionVideoDetail(photoId: $photoId, type: $type) {
                        photo {
                            id
                            caption
                            coverUrl
                            photoUrl
                            videoResource {
                                urlList {
                                    url
                                }
                            }
                            userProfile {
                                ownerName
                                headUrl
                            }
                            counts {
                                playCount
                                likeCount
                                commentCount
                            }
                        }
                    }
                }",
                "variables": {
                    "photoId": "%s",
                    "type": "PHOTO"
                }
            }
            """, photoId);
    }
}
```

#### 4. 小红书解析器 (xiaohongshu-parser)
**位置**: `uniCloud-aliyun/cloudfunctions/xiaohongshu-parser/index.js`

**核心挑战**:
- 需要处理复杂的反爬虫机制
- URL格式多样化
- 需要特殊的Cookie和请求头

**SpringBoot实现策略**:
```java
@Component
public class XiaohongshuParser implements PlatformParser {

    @Override
    public VideoInfo parse(String url) throws ParseException {
        // 小红书可能需要多种解析策略

        // 策略1: 直接HTML解析
        VideoInfo result = parseFromHtml(url);
        if (result != null) return result;

        // 策略2: 使用无头浏览器
        return parseWithWebDriver(url);
    }

    private VideoInfo parseFromHtml(String url) {
        Map<String, String> headers = Map.of(
            "User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)",
            "Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language", "zh-CN,zh;q=0.9",
            "Referer", "https://www.xiaohongshu.com/",
            "Cookie", "你需要的Cookie值"
        );

        try {
            String html = httpClientService.get(url, headers);
            return extractVideoInfoFromHtml(html);
        } catch (Exception e) {
            log.warn("小红书HTML解析失败: {}", e.getMessage());
            return null;
        }
    }

    private VideoInfo parseWithWebDriver(String url) {
        return webDriverService.executeWithBrowser(driver -> {
            driver.get(url);

            // 等待页面加载
            WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(10));
            wait.until(ExpectedConditions.presenceOfElementLocated(
                By.cssSelector("[data-testid='note-video'], [data-testid='note-image']")
            ));

            // 提取页面数据
            return extractVideoInfoFromPage(driver);
        });
    }
}
```

#### 5. B站解析器 (bilibili-parser)
**位置**: `uniCloud-aliyun/cloudfunctions/bilibili-parser/index.js`

**特点**:
- 支持多种URL格式 (BV号、AV号、短链接)
- 需要处理分P视频
- API相对稳定

**SpringBoot实现**:
```java
@Component
public class BilibiliParser implements PlatformParser {

    private static final String API_BASE = "https://api.bilibili.com/x/web-interface/view";

    @Override
    public VideoInfo parse(String url) throws ParseException {
        String videoId = extractVideoId(url);
        String apiUrl = String.format("%s?%s=%s", API_BASE,
            videoId.startsWith("BV") ? "bvid" : "aid", videoId);

        Map<String, String> headers = Map.of(
            "User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Referer", "https://www.bilibili.com/"
        );

        String response = httpClientService.get(apiUrl, headers);
        return parseBilibiliResponse(response);
    }

    private String extractVideoId(String url) {
        // 处理多种B站URL格式
        Pattern bvPattern = Pattern.compile("BV[a-zA-Z0-9]+");
        Pattern avPattern = Pattern.compile("av(\\d+)");

        Matcher bvMatcher = bvPattern.matcher(url);
        if (bvMatcher.find()) {
            return bvMatcher.group();
        }

        Matcher avMatcher = avPattern.matcher(url);
        if (avMatcher.find()) {
            return avMatcher.group(1);
        }

        throw new ParseException("无法提取B站视频ID");
    }
}
```

#### 6. 微博解析器 (weibo-parser)
**位置**: `uniCloud-aliyun/cloudfunctions/weibo-parser/index.js`

**SpringBoot实现要点**:
```java
@Component
public class WeiboParser implements PlatformParser {

    @Override
    public VideoInfo parse(String url) throws ParseException {
        // 微博需要处理登录状态和反爬虫
        String html = getWeiboPage(url);
        return extractVideoFromHtml(html);
    }

    private String getWeiboPage(String url) {
        Map<String, String> headers = Map.of(
            "User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)",
            "Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language", "zh-CN,zh;q=0.9",
            "Referer", "https://weibo.com/",
            "Cookie", "需要的Cookie值"
        );

        return httpClientService.get(url, headers);
    }
}
```

#### 7. 皮皮虾解析器 (pipix-parser)
**位置**: `uniCloud-aliyun/cloudfunctions/pipix-parser/index.js`

**特点**:
- 支持短视频解析
- 需要处理特殊的URL格式
- API相对稳定

**SpringBoot实现**:
```java
@Component
public class PipixParser implements PlatformParser {

    @Override
    public VideoInfo parse(String url) throws ParseException {
        // 1. 提取视频ID
        String videoId = extractVideoId(url);

        // 2. 构造API请求
        String apiUrl = buildPipixApiUrl(videoId);

        Map<String, String> headers = Map.of(
            "User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)",
            "Referer", "https://h5.pipix.com/"
        );

        // 3. 获取视频信息
        String response = httpClientService.get(apiUrl, headers);
        return parsePipixResponse(response);
    }

    private String extractVideoId(String url) {
        Pattern pattern = Pattern.compile("item/(\\d+)");
        Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            return matcher.group(1);
        }
        throw new ParseException("无法提取皮皮虾视频ID");
    }
}
```

#### 8. 汽水音乐解析器 (qishui-music-parser)
**位置**: `uniCloud-aliyun/cloudfunctions/qishui-music-parser/index.js`

**特点**:
- 专门解析音乐内容
- 支持音频文件下载
- 需要特殊的音乐API处理

**SpringBoot实现**:
```java
@Component
public class QishuiMusicParser implements PlatformParser {

    @Override
    public VideoInfo parse(String url) throws ParseException {
        // 汽水音乐的特殊处理逻辑
        String musicId = extractMusicId(url);

        Map<String, String> headers = Map.of(
            "User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)",
            "Referer", "https://qishui.douyin.com/"
        );

        String apiUrl = String.format("https://qishui.douyin.com/api/music/detail?id=%s", musicId);
        String response = httpClientService.get(apiUrl, headers);

        return parseMusicResponse(response);
    }
}
```

#### 9. 最右解析器 (zuiyou-parser)
**位置**: `uniCloud-aliyun/cloudfunctions/zuiyou-parser/index.js`

**特点**:
- 支持视频和图集解析
- 支持Live Photo功能
- 需要处理混合内容类型

**SpringBoot实现**:
```java
@Component
public class ZuiyouParser implements PlatformParser {

    @Override
    public VideoInfo parse(String url) throws ParseException {
        // 1. 提取帖子ID和视频ID
        Map<String, String> params = extractUrlParams(url);
        String pid = params.get("pid");
        String vid = params.get("vid");

        // 2. 获取页面HTML
        Map<String, String> headers = Map.of(
            "User-Agent", "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)",
            "Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
        );

        String html = httpClientService.get(url, headers);

        // 3. 提取JSON数据
        Pattern pattern = Pattern.compile("window\\.APP_INITIAL_STATE\\s*=\\s*({[\\s\\S]*?});");
        Matcher matcher = pattern.matcher(html);

        if (matcher.find()) {
            String jsonStr = matcher.group(1);
            return parseZuiyouJson(jsonStr, pid, vid);
        }

        throw new ParseException("无法提取最右数据");
    }

    private VideoInfo parseZuiyouJson(String jsonStr, String pid, String vid) {
        // 解析JSON，支持Live Photo和混合内容
        JsonNode jsonData = objectMapper.readTree(jsonStr);
        JsonNode postData = findPostData(jsonData, pid);

        if (postData != null) {
            return buildZuiyouVideoInfo(postData);
        }

        throw new ParseException("未找到最右视频数据");
    }
}
```

#### 10. 好看视频解析器 (haokan-parser)
**位置**: `uniCloud-aliyun/cloudfunctions/haokan-parser/index.js`

**特点**:
- 支持多清晰度视频解析
- 百度系产品，API相对稳定
- 支持完整的视频统计信息

**SpringBoot实现**:
```java
@Component
public class HaokanParser implements PlatformParser {

    @Override
    public VideoInfo parse(String url) throws ParseException {
        // 1. 提取视频ID
        String vid = extractVideoId(url);

        // 2. 获取页面数据
        Map<String, String> headers = Map.of(
            "User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
        );

        String html = httpClientService.get(url, headers);

        // 3. 提取预加载数据
        Pattern pattern = Pattern.compile("window\\.__PRELOADED_STATE__\\s*=\\s*({[\\s\\S]*?});");
        Matcher matcher = pattern.matcher(html);

        if (matcher.find()) {
            String jsonStr = matcher.group(1);
            return parseHaokanJson(jsonStr);
        }

        throw new ParseException("无法提取好看视频数据");
    }

    private VideoInfo parseHaokanJson(String jsonStr) {
        JsonNode jsonData = objectMapper.readTree(jsonStr);
        JsonNode videoMeta = jsonData.path("curVideoMeta");

        VideoInfo videoInfo = new VideoInfo();
        videoInfo.setTitle(videoMeta.path("title").asText());
        videoInfo.setAuthor(videoMeta.path("author").path("name").asText());
        videoInfo.setDuration(videoMeta.path("duration").asLong());
        videoInfo.setPlayCount(videoMeta.path("playCount").asLong());

        // 处理多清晰度
        JsonNode clarityUrls = videoMeta.path("clarityUrl");
        List<String> videoUrls = new ArrayList<>();
        clarityUrls.forEach(clarity -> {
            String videoUrl = clarity.path("url").asText();
            if (videoUrl.startsWith("http://")) {
                videoUrl = videoUrl.replace("http://", "https://");
            }
            videoUrls.add(videoUrl);
        });

        videoInfo.setVideoUrls(videoUrls);
        videoInfo.setPlatform(PlatformType.HAOKAN);

        return videoInfo;
    }
}
```

#### 11. 微视解析器 (weishi-parser) - 重点难题
**位置**: `uniCloud-aliyun/cloudfunctions/weishi-parser/index.js`

**当前问题**:
- 微视改版为SPA架构
- `feedsList` 为空数组
- 需要JavaScript执行获取动态数据

**SpringBoot解决方案**:
```java
@Component
public class WeishiParser implements PlatformParser {

    @Override
    public VideoInfo parse(String url) throws ParseException {
        // 多策略解析

        // 策略1: 传统HTML解析 (向后兼容)
        VideoInfo result = parseFromHtml(url);
        if (result != null) return result;

        // 策略2: JavaScript执行
        result = parseWithJsEngine(url);
        if (result != null) return result;

        // 策略3: 无头浏览器 (最后手段)
        return parseWithWebDriver(url);
    }

    private VideoInfo parseWithWebDriver(String url) {
        return webDriverService.executeWithBrowser(driver -> {
            driver.get(url);

            // 等待JavaScript执行完成
            WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(15));
            wait.until(d -> {
                Object result = ((JavascriptExecutor) d).executeScript(
                    "return window.Vise && window.Vise.initState && " +
                    "window.Vise.initState.feedsList && " +
                    "window.Vise.initState.feedsList.length > 0"
                );
                return Boolean.TRUE.equals(result);
            });

            // 获取动态加载的数据
            String jsonData = (String) ((JavascriptExecutor) driver).executeScript(
                "return JSON.stringify(window.Vise.initState)"
            );

            return parseWeishiJsonData(jsonData);
        });
    }
}
```

## 配置文件和环境

### 1. 数据库配置 (可选)
```yaml
spring:
  datasource:
    url: ****************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
```

### 2. 缓存配置
```java
@Configuration
@EnableCaching
public class CacheConfig {

    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(1, TimeUnit.HOURS)
            .recordStats());
        return cacheManager;
    }
}
```

### 3. 安全配置
```java
@Configuration
public class SecurityConfig {

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf().disable()
            .authorizeHttpRequests(auth -> auth
                .requestMatchers("/api/v1/parser/**").permitAll()
                .anyRequest().authenticated()
            );
        return http.build();
    }
}
```

## 测试策略

### 1. 单元测试
```java
@ExtendWith(MockitoExtension.class)
class DouyinParserTest {

    @Mock
    private HttpClientService httpClientService;

    @InjectMocks
    private DouyinParser douyinParser;

    @Test
    void testParseDouyinVideo() {
        // 模拟HTTP响应
        String mockResponse = "..."; // 真实的API响应
        when(httpClientService.get(anyString(), anyMap())).thenReturn(mockResponse);

        // 执行解析
        VideoInfo result = douyinParser.parse("https://www.douyin.com/video/123456");

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getTitle()).isNotEmpty();
        assertThat(result.getVideoUrl()).isNotEmpty();
    }
}
```

### 2. 集成测试
```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class VideoParserControllerIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;

    @Test
    void testParseVideo() {
        ParseRequest request = new ParseRequest();
        request.setUrl("https://www.douyin.com/video/123456");

        ResponseEntity<ParseResponse> response = restTemplate.postForEntity(
            "/api/v1/parser/parse", request, ParseResponse.class);

        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.OK);
        assertThat(response.getBody().isSuccess()).isTrue();
    }
}
```

## 监控和运维

### 1. 健康检查
```java
@Component
public class ParserHealthIndicator implements HealthIndicator {

    @Override
    public Health health() {
        // 检查各个解析器的健康状态
        boolean allParsersHealthy = checkAllParsers();

        if (allParsersHealthy) {
            return Health.up()
                .withDetail("parsers", "All parsers are working")
                .build();
        } else {
            return Health.down()
                .withDetail("parsers", "Some parsers are failing")
                .build();
        }
    }
}
```

### 2. 指标监控
```java
@Component
public class ParserMetrics {

    private final MeterRegistry meterRegistry;
    private final Counter parseSuccessCounter;
    private final Counter parseFailureCounter;
    private final Timer parseTimer;

    public ParserMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.parseSuccessCounter = Counter.builder("parser.success")
            .description("Number of successful parses")
            .register(meterRegistry);
        this.parseFailureCounter = Counter.builder("parser.failure")
            .description("Number of failed parses")
            .register(meterRegistry);
        this.parseTimer = Timer.builder("parser.duration")
            .description("Parse duration")
            .register(meterRegistry);
    }

    public void recordSuccess(String platform) {
        parseSuccessCounter.increment(Tags.of("platform", platform));
    }

    public void recordFailure(String platform, String error) {
        parseFailureCounter.increment(Tags.of("platform", platform, "error", error));
    }
}
```

## 部署脚本

### 1. Docker Compose
```yaml
version: '3.8'
services:
  video-parser:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
    volumes:
      - ./logs:/app/logs

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: video_parser
    volumes:
      - mysql_data:/var/lib/mysql

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
```

### 2. Kubernetes部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: video-parser
spec:
  replicas: 3
  selector:
    matchLabels:
      app: video-parser
  template:
    metadata:
      labels:
        app: video-parser
    spec:
      containers:
      - name: video-parser
        image: video-parser:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
---
apiVersion: v1
kind: Service
metadata:
  name: video-parser-service
spec:
  selector:
    app: video-parser
  ports:
  - port: 80
    targetPort: 8080
  type: LoadBalancer
```

## 总结

这个文档提供了从uniCloud迁移到SpringBoot的完整指南，包括：

1. **架构设计** - 清晰的分层架构和接口设计
2. **技术选型** - 经过验证的技术栈组合
3. **实现细节** - 每个平台解析器的具体实现方法
4. **性能优化** - 缓存、连接池、异步处理等优化策略
5. **运维支持** - 监控、日志、部署等运维相关配置
6. **测试策略** - 完整的测试覆盖方案

特别针对微视等SPA架构的平台，提供了JavaScript执行和无头浏览器的解决方案，能够有效解决当前uniCloud环境的限制。

将这个文档交给AI开发者，可以快速实现一个功能完整、性能优良的SpringBoot版本解析器。
