# 后端配置说明

## 配置文件位置

**文件路径**: `components/backend-config.js`

这是项目的后端配置文件，开发者通过修改此文件来切换后端类型和配置服务器地址。

## 配置选项

### 1. 后端类型切换

```javascript
const backendConfig = {
  // 当前使用的后端类型: 'unicloud' | 'springboot'
  // 开发者修改此处来切换后端
  currentBackend: 'unicloud',  // 👈 修改这里
}
```

**可选值**:
- `'unicloud'` - 使用uniCloud云函数后端
- `'springboot'` - 使用SpringBoot REST API后端

### 2. SpringBoot服务器配置

```javascript
springboot: {
  // 服务器配置 - 开发者在此配置SpringBoot服务器地址
  server: {
    // 开发环境配置 (本地测试)
    development: {
      baseUrl: 'http://localhost:8080',  // 👈 本地开发服务器地址
      apiPrefix: '/api/v1',
      timeout: 30000,
      description: '本地开发服务器'
    },
    
    // 生产环境配置 (云服务器)
    production: {
      baseUrl: 'https://your-domain.com',  // 👈 修改为你的云服务器域名
      apiPrefix: '/api/v1',
      timeout: 30000,
      description: '生产环境服务器'
    }
  },
  
  // 当前使用的环境: 'development' | 'production'
  // 开发者修改此处来切换开发/生产环境
  currentEnv: 'development',  // 👈 修改这里
}
```

### 3. API认证配置

```javascript
// API认证配置
auth: {
  enabled: true,
  masterKey: 'ClearMark-Master-2025-SecureKey-9527',  // 👈 可以修改主密钥
  apiKey: '',  // 运行时自动生成
  storageKey: 'springboot_api_key'
}
```

## 配置场景

### 场景1: 使用uniCloud后端 (默认)

```javascript
const backendConfig = {
  currentBackend: 'unicloud',  // 使用uniCloud
  // 其他配置保持不变
}
```

**特点**:
- 无需配置服务器地址
- 使用uniCloud云函数
- 免费且稳定
- 适合小型项目

### 场景2: 本地开发SpringBoot

```javascript
const backendConfig = {
  currentBackend: 'springboot',  // 切换到SpringBoot
  springboot: {
    currentEnv: 'development',   // 使用开发环境
    server: {
      development: {
        baseUrl: 'http://localhost:8080',  // 本地SpringBoot服务
        // 其他配置...
      }
    }
  }
}
```

**使用步骤**:
1. 启动本地SpringBoot服务 (端口8080)
2. 修改配置文件如上
3. 重新编译UniApp项目
4. 系统自动连接本地SpringBoot服务

### 场景3: 生产环境SpringBoot

```javascript
const backendConfig = {
  currentBackend: 'springboot',  // 切换到SpringBoot
  springboot: {
    currentEnv: 'production',    // 使用生产环境
    server: {
      production: {
        baseUrl: 'https://api.yourdomain.com',  // 你的云服务器域名
        // 其他配置...
      }
    }
  }
}
```

**使用步骤**:
1. 部署SpringBoot到云服务器
2. 配置域名和SSL证书
3. 修改配置文件如上
4. 重新编译并发布UniApp项目

## 配置修改步骤

### 1. 切换到SpringBoot后端

1. 打开 `components/backend-config.js`
2. 找到 `currentBackend` 配置项
3. 修改为 `'springboot'`
4. 保存文件

### 2. 配置SpringBoot服务器地址

1. 在同一文件中找到 `springboot.server` 配置
2. 修改对应环境的 `baseUrl`
   - 开发环境: `http://localhost:8080`
   - 生产环境: `https://your-domain.com`
3. 保存文件

### 3. 切换开发/生产环境

1. 找到 `springboot.currentEnv` 配置项
2. 修改为 `'development'` 或 `'production'`
3. 保存文件

### 4. 重新编译项目

修改配置后需要重新编译项目:
```bash
# HBuilder X: 重新运行项目
# 或者使用命令行
npm run dev:mp-weixin
```

## 注意事项

### 1. 配置文件安全
- 不要将敏感信息提交到版本控制
- 生产环境的域名和密钥要妥善保管
- 可以使用环境变量来管理敏感配置

### 2. 网络配置
- 确保SpringBoot服务器端口开放
- 配置正确的CORS策略
- 生产环境建议使用HTTPS

### 3. API Key管理
- API Key会自动生成和管理
- 如果连接失败，系统会自动重新生成
- 可以修改 `masterKey` 来增强安全性

### 4. 调试信息
配置修改后，可以在控制台看到初始化信息:
```
🚀 后端配置初始化完成: {
  当前后端: "springboot",
  SpringBoot环境: "development", 
  配置来源: "开发者配置文件"
}
```

## 常见问题

### Q: 修改配置后没有生效？
A: 需要重新编译项目，配置是在编译时确定的。

### Q: SpringBoot连接失败？
A: 检查服务器地址、端口、网络连接和CORS配置。

### Q: API Key无效？
A: 系统会自动重新生成，或者检查 `masterKey` 配置。

### Q: 如何在不同环境使用不同配置？
A: 可以创建多个配置文件，或使用构建脚本动态替换配置。

## 高级配置

### 1. 多环境配置文件

可以创建多个配置文件:
- `backend-config.dev.js` - 开发环境
- `backend-config.prod.js` - 生产环境

### 2. 环境变量配置

```javascript
const backendConfig = {
  currentBackend: process.env.BACKEND_TYPE || 'unicloud',
  springboot: {
    currentEnv: process.env.NODE_ENV === 'production' ? 'production' : 'development',
    server: {
      production: {
        baseUrl: process.env.API_BASE_URL || 'https://api.yourdomain.com'
      }
    }
  }
}
```

### 3. 动态配置加载

```javascript
// 根据编译环境动态选择配置
const isDev = process.env.NODE_ENV === 'development'
const backendConfig = {
  currentBackend: isDev ? 'unicloud' : 'springboot',
  // ...
}
```

这样就可以实现开发环境自动使用uniCloud，生产环境自动使用SpringBoot。
