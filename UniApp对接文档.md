# ClearMark API 对接文档

## 概述

ClearMark 提供 RESTful API 接口，支持多平台视频解析。本文档说明接口调用方法、请求参数和返回格式。

**服务地址**: `http://localhost:8080`
**API前缀**: `/api/v1`

## 🔐 认证方式

使用 API Key 进行身份认证，在请求头中添加：
```
X-API-Key: your-api-key-here
```

## 📋 API 接口列表

### 1. 生成 API Key

**接口**: `POST /api/v1/auth/generate-key`
**说明**: 生成新的API Key（需要主密钥）

**请求头**:
```
X-Master-Key: ClearMark-Master-2025-SecureKey-9527
```

**请求示例**:
```javascript
uni.request({
  url: 'http://localhost:8080/api/v1/auth/generate-key',
  method: 'POST',
  header: {
    'X-Master-Key': 'ClearMark-Master-2025-SecureKey-9527'
  },
  success: (res) => {
    console.log(res.data);
  }
});
```

**成功响应**:
```json
{
  "success": true,
  "message": "API Key生成成功",
  "data": {
    "apiKey": "Vz5UdFO5YM81ISO5eXqKkS4gP6WhdQCCwE5GHN67dzw",
    "usage": "请在请求头中添加 X-API-Key: Vz5UdFO5YM81ISO5eXqKkS4gP6WhdQCCwE5GHN67dzw"
  },
  "timestamp": 1756375649878
}
```

**失败响应**:
```json
{
  "success": false,
  "message": "无效的主密钥，无法生成API Key",
  "timestamp": 1756375642701
}
```

### 2. 解析视频

**接口**: `POST /api/v1/parser/parse`
**说明**: 解析视频URL，获取视频信息和下载链接

**请求头**:
```
Content-Type: application/json
X-API-Key: your-api-key-here
```

**请求参数**:
```json
{
  "url": "https://b23.tv/u8OjgCS"
}
```

**请求示例**:
```javascript
uni.request({
  url: 'http://localhost:8080/api/v1/parser/parse',
  method: 'POST',
  header: {
    'Content-Type': 'application/json',
    'X-API-Key': 'Vz5UdFO5YM81ISO5eXqKkS4gP6WhdQCCwE5GHN67dzw'
  },
  data: {
    url: 'https://b23.tv/u8OjgCS'
  },
  success: (res) => {
    console.log(res.data);
  }
});
```

**成功响应**:
```json
{
  "success": true,
  "message": "解析成功",
  "data": {
    "title": "【老司机必备】Deepseek与Grok的破甲指令",
    "author": "爱吃西瓜的萱萱",
    "content": "全网首发！五大AI引擎协同平台正式上线...",
    "type": "video",
    "platform": "bilibili",
    "source": "B站",
    "coverUrl": "https://i1.hdslb.com/bfs/archive/8b4955aa785ee0051d05f96ea4559f9e269e806f.jpg",
    "originalUrl": "https://www.bilibili.com/video/BV1W5tBzvETG",
    "processedData": {
      "data": "https://upos-sz-estghw.bilivideo.com/upgcxcode/18/08/31539530818/31539530818-1-30032.m4s",
      "isUrl": true,
      "type": "video/mp4",
      "duration": 164,
      "videoUrls": ["https://upos-sz-estghw.bilivideo.com/upgcxcode/18/08/31539530818/31539530818-1-30032.m4s"],
      "imageUrls": [],
      "coverUrl": "https://i1.hdslb.com/bfs/archive/8b4955aa785ee0051d05f96ea4559f9e269e806f.jpg",
      "hasDirectUrl": true,
      "requiresProxy": false
    },
    "playCount": 12049,
    "likeCount": 564,
    "commentCount": 215,
    "shareCount": 10,
    "createTime": 1754546774000,
    "timestamp": 1756375660971,
    "version": "统一解析器 v1.0.0"
  }
}
```

**图集/Live Photo响应示例** (小红书/抖音/最右):
```json
{
  "success": true,
  "message": "解析成功",
  "data": {
    "title": "今日穿搭分享",
    "author": "时尚博主",
    "content": "今天的穿搭很喜欢，分享给大家~",
    "type": "image",
    "platform": "xiaohongshu",
    "source": "小红书",
    "coverUrl": "https://sns-img-qc.xhscdn.com/spectrum/1040g0k30s2h4k8lm0604a0p9ava08cqg8kag8o0",
    "originalUrl": "https://www.xiaohongshu.com/explore/xxx",
    "processedData": {
      "data": "https://sns-img-qc.xhscdn.com/spectrum/1040g0k30s2h4k8lm0604a0p9ava08cqg8kag8o0",
      "isUrl": true,
      "type": "live_photo",
      "imageUrls": [
        "https://sns-img-qc.xhscdn.com/spectrum/1040g0k30s2h4k8lm0604a0p9ava08cqg8kag8o0",
        "https://sns-img-qc.xhscdn.com/spectrum/1040g0k30s2h4k8lm0604a0p9ava08cqg8kag8o1"
      ],
      "videoUrls": [
        "https://sns-video-qc.xhscdn.com/stream/110/259/01e4a4a50c806e10010370037f6c5c95a2_259.mp4"
      ],
      "livePhotoVideos": [
        "https://sns-video-qc.xhscdn.com/stream/110/259/01e4a4a50c806e10010370037f6c5c95a2_259.mp4",
        null
      ],
      "hasLivePhoto": true,
      "isImageContent": true,
      "count": 2
    },
    "likeCount": 1234,
    "commentCount": 56,
    "shareCount": 78,
    "createTime": 1754546774000,
    "timestamp": 1756375660971,
    "version": "统一解析器 v1.0.0"
  }
}
```

**音乐响应示例** (汽水音乐):
```json
{
  "success": true,
  "message": "解析成功",
  "data": {
    "title": "热门音乐MV",
    "author": "知名歌手",
    "content": "最新发布的音乐MV",
    "type": "video",
    "platform": "qishui",
    "source": "汽水音乐",
    "coverUrl": "https://p3-dy.byteimg.com/img/tos-cn-p-0000/cover.jpg",
    "originalUrl": "https://qishui.douyin.com/xxx",
    "processedData": {
      "data": "https://v3-dy-o.zjcdn.com/xxx.mp4",
      "isUrl": true,
      "type": "video/mp4",
      "duration": 180,
      "videoUrls": ["https://v3-dy-o.zjcdn.com/xxx.mp4"],
      "imageUrls": [],
      "hasDirectUrl": true
    },
    "playCount": 50000,
    "likeCount": 2000,
    "commentCount": 300,
    "createTime": 1754546774000,
    "timestamp": 1756375660971,
    "version": "统一解析器 v1.0.0"
  }
}
```

**多清晰度视频响应示例** (好看视频):
```json
{
  "success": true,
  "message": "解析成功",
  "data": {
    "title": "精彩短视频内容",
    "author": "创作者昵称",
    "content": "视频描述内容",
    "type": "video",
    "platform": "haokan",
    "source": "好看视频",
    "coverUrl": "https://vd3.bdstatic.com/mda-xxx/cover.jpg",
    "originalUrl": "https://haokan.baidu.com/v?vid=xxx",
    "processedData": {
      "data": "https://vd3.bdstatic.com/mda-xxx/1080p.mp4",
      "isUrl": true,
      "type": "video/mp4",
      "duration": 30,
      "videoUrls": [
        "https://vd3.bdstatic.com/mda-xxx/1080p.mp4"
      ],
      "qualityUrls": [
        {
          "quality": "蓝光",
          "url": "https://vd3.bdstatic.com/mda-xxx/1080p.mp4",
          "size": 15.2,
          "bitrate": 955
        },
        {
          "quality": "超清",
          "url": "https://vd3.bdstatic.com/mda-xxx/720p.mp4",
          "size": 8.5,
          "bitrate": 475
        },
        {
          "quality": "高清",
          "url": "https://vd3.bdstatic.com/mda-xxx/576p.mp4",
          "size": 6.2,
          "bitrate": 375
        },
        {
          "quality": "标清",
          "url": "https://vd3.bdstatic.com/mda-xxx/360p.mp4",
          "size": 4.1,
          "bitrate": 244
        }
      ],
      "imageUrls": [],
      "isH5Friendly": true,
      "requiresProxy": false
    },
    "playCount": 25000,
    "duration": 30,
    "publishTime": 1754546774000,
    "createTime": 1754546774000,
    "timestamp": 1756375660971,
    "version": "统一解析器 v1.0.0"
  }
}
```

**失败响应**:
```json
{
  "success": false,
  "message": "解析失败: 不支持的平台",
  "timestamp": 1756375660971
}
```

### 3. 检测平台类型

**接口**: `GET /api/v1/parser/detect`
**说明**: 根据URL自动检测对应的平台类型

**请求头**:
```
X-API-Key: your-api-key-here
```

**请求参数**:
```
url: 视频链接 (URL编码)
```

**请求示例**:
```javascript
uni.request({
  url: 'http://localhost:8080/api/v1/parser/detect?url=' + encodeURIComponent('https://b23.tv/u8OjgCS'),
  method: 'GET',
  header: {
    'X-API-Key': 'Vz5UdFO5YM81ISO5eXqKkS4gP6WhdQCCwE5GHN67dzw'
  },
  success: (res) => {
    console.log(res.data);
  }
});
```

**成功响应**:
```json
"bilibili"
```

### 4. 获取支持的平台

**接口**: `GET /api/v1/parser/platforms`
**说明**: 获取所有支持的视频平台列表（公开接口，无需认证）

**请求示例**:
```javascript
uni.request({
  url: 'http://localhost:8080/api/v1/parser/platforms',
  method: 'GET',
  success: (res) => {
    console.log(res.data);
  }
});
```

**成功响应**:
```json
["douyin","kuaishou","xiaohongshu","bilibili","weibo","weishi","pipix","qishui","zuiyou","haokan"]
```

### 5. 健康检查

**接口**: `GET /api/v1/parser/health`
**说明**: 检查服务是否正常运行（公开接口，无需认证）

**请求示例**:
```javascript
uni.request({
  url: 'http://localhost:8080/api/v1/parser/health',
  method: 'GET',
  success: (res) => {
    console.log(res.data);
  }
});
```

**成功响应**:
```
OK
```

## 📱 支持的平台

| 平台 | 平台代码 | 支持状态 | 说明 |
|------|----------|----------|------|
| 抖音 | douyin | ✅ | 支持视频和图集，包括Live Photo |
| 快手 | kuaishou | ✅ | 支持视频解析 |
| 小红书 | xiaohongshu | ✅ | 支持视频和图集，包括Live Photo |
| B站 | bilibili | ✅ | 支持视频解析 |
| 微博 | weibo | ✅ | 支持视频和图集解析 |
| 微视 | weishi | ✅ | 支持视频解析 |
| 皮皮虾 | pipix | ✅ | 支持视频解析 |
| 汽水音乐 | qishui | ✅ | 支持音乐解析 |
| 最右 | zuiyou | ✅ | 支持视频和图集，包括Live Photo |
| 好看视频 | haokan | ✅ | 支持视频解析，多清晰度 |

## ⚠️ 错误码说明

| HTTP状态码 | 说明 |
|------------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 403 | API Key无效或已过期 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 📝 注意事项

1. **API Key管理**: 请妥善保管API Key，避免泄露
2. **请求频率**: 建议控制请求频率，避免被限流
3. **错误处理**: 请根据返回的错误信息进行相应处理
4. **URL格式**: 支持原链接和短链接格式
5. **视频下载**: 返回的videoUrl可直接用于下载或播放

## 🚀 快速开始

### UniApp 请求示例

```javascript
// 解析视频的完整示例
function parseVideo(url) {
  uni.request({
    url: 'http://localhost:8080/api/v1/parser/parse',
    method: 'POST',
    header: {
      'Content-Type': 'application/json',
      'X-API-Key': 'your-api-key-here'
    },
    data: {
      url: url
    },
    success: (res) => {
      if (res.data.success) {
        console.log('解析成功:', res.data.data);
        // 处理视频信息
        const videoInfo = res.data.data;
        // videoInfo.title - 视频标题
        // videoInfo.videoUrl - 视频下载链接
        // videoInfo.cover - 视频封面
      } else {
        console.error('解析失败:', res.data.message);
      }
    },
    fail: (err) => {
      console.error('请求失败:', err);
    }
  });
}
```

---

**更新时间**: 2025-08-28
**版本**: v2.0.0 (支持安全认证)
