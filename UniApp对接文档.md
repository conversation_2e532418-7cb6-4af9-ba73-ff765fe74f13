# ClearMark API 对接文档

## 概述

ClearMark 提供 RESTful API 接口，支持多平台视频解析。本文档说明接口调用方法、请求参数和返回格式。

**服务地址**: `http://localhost:8080`
**API前缀**: `/api/v1`

## 🔐 认证方式

使用 API Key 进行身份认证，在请求头中添加：
```
X-API-Key: your-api-key-here
```

## 📋 API 接口列表

### 1. 生成 API Key

**接口**: `POST /api/v1/auth/generate-key`
**说明**: 生成新的API Key（需要主密钥）

**请求头**:
```
X-Master-Key: ClearMark-Master-2025-SecureKey-9527
```

**请求示例**:
```javascript
uni.request({
  url: 'http://localhost:8080/api/v1/auth/generate-key',
  method: 'POST',
  header: {
    'X-Master-Key': 'ClearMark-Master-2025-SecureKey-9527'
  },
  success: (res) => {
    console.log(res.data);
  }
});
```

**成功响应**:
```json
{
  "success": true,
  "message": "API Key生成成功",
  "data": {
    "apiKey": "Vz5UdFO5YM81ISO5eXqKkS4gP6WhdQCCwE5GHN67dzw",
    "usage": "请在请求头中添加 X-API-Key: Vz5UdFO5YM81ISO5eXqKkS4gP6WhdQCCwE5GHN67dzw"
  },
  "timestamp": 1756375649878
}
```

**失败响应**:
```json
{
  "success": false,
  "message": "无效的主密钥，无法生成API Key",
  "timestamp": 1756375642701
}
```

### 2. 解析视频

**接口**: `POST /api/v1/parser/parse`
**说明**: 解析视频URL，获取视频信息和下载链接

**请求头**:
```
Content-Type: application/json
X-API-Key: your-api-key-here
```

**请求参数**:
```json
{
  "url": "https://b23.tv/u8OjgCS"
}
```

**请求示例**:
```javascript
uni.request({
  url: 'http://localhost:8080/api/v1/parser/parse',
  method: 'POST',
  header: {
    'Content-Type': 'application/json',
    'X-API-Key': 'Vz5UdFO5YM81ISO5eXqKkS4gP6WhdQCCwE5GHN67dzw'
  },
  data: {
    url: 'https://b23.tv/u8OjgCS'
  },
  success: (res) => {
    console.log(res.data);
  }
});
```

**统一成功响应格式**:
```json
{
  "success": true,
  "message": "解析成功",
  "data": {
    // === 基本信息 (所有平台必须) ===
    "title": "内容标题",
    "author": "作者名称",
    "content": "内容描述",
    "type": "video|image",
    "platform": "平台代码(小写)",
    "source": "平台显示名称",
    "coverUrl": "封面图片URL",
    "originalUrl": "原始链接",

    // === 核心数据 (所有平台必须) ===
    "processedData": {
      "data": "主要内容URL(视频URL或第一张图片URL)",
      "isUrl": true,
      "type": "video/mp4|image/jpeg|live_photo",

      // === 视频相关字段 ===
      "duration": 164,                    // 视频时长(秒)，视频类型必须
      "videoUrls": ["视频URL1", "视频URL2"], // 视频URL数组，视频类型必须

      // === 图片相关字段 ===
      "imageUrls": ["图片URL1", "图片URL2"], // 图片URL数组，图片类型必须
      "count": 2,                         // 图片数量，图片类型时提供

      // === Live Photo相关字段 (可选) ===
      "hasLivePhoto": true,               // 是否包含Live Photo
      "livePhotoVideos": ["视频URL", null], // 与图片一一对应的背景视频，null表示该图片无Live Photo
      "isImageContent": true,             // 标识为图片内容



    },

    // === 系统信息 (必须) ===
    "timestamp": 1756375660971            // 解析时间戳
  }
}
```

**具体示例**:

1. **视频内容示例** (B站/抖音/快手等):
```json
{
  "success": true,
  "message": "解析成功",
  "data": {
    "title": "精彩视频标题",
    "author": "创作者",
    "content": "视频描述",
    "type": "video",
    "platform": "bilibili",
    "source": "B站",
    "coverUrl": "https://example.com/cover.jpg",
    "originalUrl": "https://www.bilibili.com/video/xxx",
    "processedData": {
      "data": "https://example.com/video.mp4",
      "isUrl": true,
      "type": "video/mp4",
      "duration": 164,
      "videoUrls": ["https://example.com/video.mp4"],
      "imageUrls": []
    },
    "timestamp": 1756375660971
  }
}
```

2. **图集内容示例** (小红书/微博等):
```json
{
  "success": true,
  "message": "解析成功",
  "data": {
    "title": "美食分享",
    "author": "美食博主",
    "content": "今天的美食推荐",
    "type": "image",
    "platform": "xiaohongshu",
    "source": "小红书",
    "coverUrl": "https://example.com/cover.jpg",
    "originalUrl": "https://www.xiaohongshu.com/explore/xxx",
    "processedData": {
      "data": "https://example.com/image1.jpg",
      "isUrl": true,
      "type": "image/jpeg",
      "videoUrls": [],
      "imageUrls": ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
      "count": 2
    },
    "timestamp": 1756375660971
  }
}
```

3. **Live Photo示例** (抖音/小红书/最右等):
```json
{
  "success": true,
  "message": "解析成功",
  "data": {
    "title": "Live Photo分享",
    "author": "摄影师",
    "content": "动态照片效果",
    "type": "image",
    "platform": "xiaohongshu",
    "source": "小红书",
    "coverUrl": "https://example.com/cover.jpg",
    "originalUrl": "https://www.xiaohongshu.com/explore/xxx",
    "processedData": {
      "data": "https://example.com/image1.jpg",
      "isUrl": true,
      "type": "live_photo",
      "videoUrls": ["https://example.com/live1.mp4"],
      "imageUrls": ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
      "hasLivePhoto": true,
      "livePhotoVideos": ["https://example.com/live1.mp4", null],
      "isImageContent": true,
      "count": 2
    },
    "timestamp": 1756375660971
  }
}
```

**失败响应**:
```json
{
  "success": false,
  "message": "解析失败: 不支持的平台",
  "timestamp": 1756375660971
}
```

### 3. 检测平台类型

**接口**: `GET /api/v1/parser/detect`
**说明**: 根据URL自动检测对应的平台类型

**请求头**:
```
X-API-Key: your-api-key-here
```

**请求参数**:
```
url: 视频链接 (URL编码)
```

**请求示例**:
```javascript
uni.request({
  url: 'http://localhost:8080/api/v1/parser/detect?url=' + encodeURIComponent('https://b23.tv/u8OjgCS'),
  method: 'GET',
  header: {
    'X-API-Key': 'Vz5UdFO5YM81ISO5eXqKkS4gP6WhdQCCwE5GHN67dzw'
  },
  success: (res) => {
    console.log(res.data);
  }
});
```

**成功响应**:
```json
"bilibili"
```

### 4. 获取支持的平台

**接口**: `GET /api/v1/parser/platforms`
**说明**: 获取所有支持的视频平台列表（公开接口，无需认证）

**请求示例**:
```javascript
uni.request({
  url: 'http://localhost:8080/api/v1/parser/platforms',
  method: 'GET',
  success: (res) => {
    console.log(res.data);
  }
});
```

**成功响应**:
```json
["douyin","kuaishou","xiaohongshu","bilibili","weibo","weishi","pipix","qishui","zuiyou","haokan"]
```

### 5. 健康检查

**接口**: `GET /api/v1/parser/health`
**说明**: 检查服务是否正常运行（公开接口，无需认证）

**请求示例**:
```javascript
uni.request({
  url: 'http://localhost:8080/api/v1/parser/health',
  method: 'GET',
  success: (res) => {
    console.log(res.data);
  }
});
```

**成功响应**:
```
OK
```

## 📱 支持的平台

| 平台 | 平台代码 | 支持状态 | 说明 |
|------|----------|----------|------|
| 抖音 | douyin | ✅ | 支持视频和图集，包括Live Photo |
| 快手 | kuaishou | ✅ | 支持视频解析 |
| 小红书 | xiaohongshu | ✅ | 支持视频和图集，包括Live Photo |
| B站 | bilibili | ✅ | 支持视频解析 |
| 微博 | weibo | ✅ | 支持视频和图集解析 |
| 微视 | weishi | ✅ | 支持视频解析 |
| 皮皮虾 | pipix | ✅ | 支持视频解析 |
| 汽水音乐 | qishui | ✅ | 支持音乐解析 |
| 最右 | zuiyou | ✅ | 支持视频和图集，包括Live Photo |
| 好看视频 | haokan | ✅ | 支持视频解析，多清晰度 |

## ⚠️ 错误码说明

| HTTP状态码 | 说明 |
|------------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 403 | API Key无效或已过期 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 📝 注意事项

1. **API Key管理**: 请妥善保管API Key，避免泄露
2. **请求频率**: 建议控制请求频率，避免被限流
3. **错误处理**: 请根据返回的错误信息进行相应处理
4. **URL格式**: 支持原链接和短链接格式
5. **视频下载**: 返回的videoUrl可直接用于下载或播放

## 🚀 快速开始

### UniApp 请求示例

```javascript
// 解析视频的完整示例
function parseVideo(url) {
  uni.request({
    url: 'http://localhost:8080/api/v1/parser/parse',
    method: 'POST',
    header: {
      'Content-Type': 'application/json',
      'X-API-Key': 'your-api-key-here'
    },
    data: {
      url: url
    },
    success: (res) => {
      if (res.data.success) {
        console.log('解析成功:', res.data.data);
        // 处理视频信息
        const videoInfo = res.data.data;
        // videoInfo.title - 视频标题
        // videoInfo.videoUrl - 视频下载链接
        // videoInfo.cover - 视频封面
      } else {
        console.error('解析失败:', res.data.message);
      }
    },
    fail: (err) => {
      console.error('请求失败:', err);
    }
  });
}
```

---

**更新时间**: 2025-08-28
**版本**: v2.0.0 (支持安全认证)
