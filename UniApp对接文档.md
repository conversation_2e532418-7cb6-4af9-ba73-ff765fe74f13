# ClearMark API 对接文档

## 概述

ClearMark 提供 RESTful API 接口，支持多平台视频解析。本文档说明接口调用方法、请求参数和返回格式。

**服务地址**: `http://localhost:8080`
**API前缀**: `/api/v1`

## 🔐 认证方式

使用 API Key 进行身份认证，在请求头中添加：
```
X-API-Key: your-api-key-here
```

## 📋 API 接口列表

### 1. 生成 API Key

**接口**: `POST /api/v1/auth/generate-key`
**说明**: 生成新的API Key（需要主密钥）

**请求头**:
```
X-Master-Key: ClearMark-Master-2025-SecureKey-9527
```

**请求示例**:
```javascript
uni.request({
  url: 'http://localhost:8080/api/v1/auth/generate-key',
  method: 'POST',
  header: {
    'X-Master-Key': 'ClearMark-Master-2025-SecureKey-9527'
  },
  success: (res) => {
    console.log(res.data);
  }
});
```

**成功响应**:
```json
{
  "success": true,
  "message": "API Key生成成功",
  "data": {
    "apiKey": "Vz5UdFO5YM81ISO5eXqKkS4gP6WhdQCCwE5GHN67dzw",
    "usage": "请在请求头中添加 X-API-Key: Vz5UdFO5YM81ISO5eXqKkS4gP6WhdQCCwE5GHN67dzw"
  },
  "timestamp": 1756375649878
}
```

**失败响应**:
```json
{
  "success": false,
  "message": "无效的主密钥，无法生成API Key",
  "timestamp": 1756375642701
}
```

### 2. 解析视频

**接口**: `POST /api/v1/parser/parse`
**说明**: 解析视频URL，获取视频信息和下载链接

**请求头**:
```
Content-Type: application/json
X-API-Key: your-api-key-here
```

**请求参数**:
```json
{
  "url": "https://b23.tv/u8OjgCS"
}
```

**请求示例**:
```javascript
uni.request({
  url: 'http://localhost:8080/api/v1/parser/parse',
  method: 'POST',
  header: {
    'Content-Type': 'application/json',
    'X-API-Key': 'Vz5UdFO5YM81ISO5eXqKkS4gP6WhdQCCwE5GHN67dzw'
  },
  data: {
    url: 'https://b23.tv/u8OjgCS'
  },
  success: (res) => {
    console.log(res.data);
  }
});
```

**成功响应**:
```json
{
  "success": true,
  "message": "解析成功",
  "data": {
    "title": "【老司机必备】Deepseek与Grok的破甲指令",
    "author": "爱吃西瓜的萱萱",
    "authorId": "627314800",
    "avatar": "https://i1.hdslb.com/bfs/face/2416493af2155b851bfdda270adf37e55ae75847.jpg",
    "cover": "http://i1.hdslb.com/bfs/archive/8b4955aa785ee0051d05f96ea4559f9e269e806f.jpg",
    "videoUrl": "https://upos-sz-estghw.bilivideo.com/upgcxcode/18/08/31539530818/31539530818-1-30032.m4s",
    "duration": 164,
    "playCount": 12049,
    "likeCount": 564,
    "commentCount": 215,
    "shareCount": 10,
    "createTime": 1754546774000,
    "description": "全网首发！五大AI引擎协同平台正式上线...",
    "platform": "BILIBILI",
    "originalUrl": "https://www.bilibili.com/video/BV1W5tBzvETG"
  },
  "timestamp": 1756375660971
}
```

**失败响应**:
```json
{
  "success": false,
  "message": "解析失败: 不支持的平台",
  "timestamp": 1756375660971
}
```

### 3. 检测平台类型

**接口**: `GET /api/v1/parser/detect`
**说明**: 根据URL自动检测对应的平台类型

**请求头**:
```
X-API-Key: your-api-key-here
```

**请求参数**:
```
url: 视频链接 (URL编码)
```

**请求示例**:
```javascript
uni.request({
  url: 'http://localhost:8080/api/v1/parser/detect?url=' + encodeURIComponent('https://b23.tv/u8OjgCS'),
  method: 'GET',
  header: {
    'X-API-Key': 'Vz5UdFO5YM81ISO5eXqKkS4gP6WhdQCCwE5GHN67dzw'
  },
  success: (res) => {
    console.log(res.data);
  }
});
```

**成功响应**:
```json
"BILIBILI"
```

### 4. 获取支持的平台

**接口**: `GET /api/v1/parser/platforms`
**说明**: 获取所有支持的视频平台列表（公开接口，无需认证）

**请求示例**:
```javascript
uni.request({
  url: 'http://localhost:8080/api/v1/parser/platforms',
  method: 'GET',
  success: (res) => {
    console.log(res.data);
  }
});
```

**成功响应**:
```json
["DOUYIN","KUAISHOU","XIAOHONGSHU","BILIBILI","WEIBO","WEISHI","PIPIX","QISHUI_MUSIC","ZUIYOU","HAOKAN"]
```

### 5. 健康检查

**接口**: `GET /api/v1/parser/health`
**说明**: 检查服务是否正常运行（公开接口，无需认证）

**请求示例**:
```javascript
uni.request({
  url: 'http://localhost:8080/api/v1/parser/health',
  method: 'GET',
  success: (res) => {
    console.log(res.data);
  }
});
```

**成功响应**:
```
OK
```

## 📱 支持的平台

| 平台 | 平台代码 | 支持状态 | 说明 |
|------|----------|----------|------|
| 抖音 | DOUYIN | ✅ | 支持Live Photo |
| 快手 | KUAISHOU | ✅ | 支持多种URL格式 |
| 小红书 | XIAOHONGSHU | ✅ | 支持图文和视频 |
| B站 | BILIBILI | ✅ | 支持视频和番剧 |
| 微博 | WEIBO | ✅ | 支持视频微博 |
| 微视 | WEISHI | ✅ | 腾讯微视 |
| 皮皮虾 | PIPIX | ✅ | 支持短链接 |
| 汽水音乐 | QISHUI_MUSIC | ✅ | 抖音音乐 |
| 最右 | ZUIYOU | ✅ | 最右App |
| 好看视频 | HAOKAN | ✅ | 百度好看视频 |

## ⚠️ 错误码说明

| HTTP状态码 | 说明 |
|------------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 403 | API Key无效或已过期 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 📝 注意事项

1. **API Key管理**: 请妥善保管API Key，避免泄露
2. **请求频率**: 建议控制请求频率，避免被限流
3. **错误处理**: 请根据返回的错误信息进行相应处理
4. **URL格式**: 支持原链接和短链接格式
5. **视频下载**: 返回的videoUrl可直接用于下载或播放

## 🚀 快速开始

### UniApp 请求示例

```javascript
// 解析视频的完整示例
function parseVideo(url) {
  uni.request({
    url: 'http://localhost:8080/api/v1/parser/parse',
    method: 'POST',
    header: {
      'Content-Type': 'application/json',
      'X-API-Key': 'your-api-key-here'
    },
    data: {
      url: url
    },
    success: (res) => {
      if (res.data.success) {
        console.log('解析成功:', res.data.data);
        // 处理视频信息
        const videoInfo = res.data.data;
        // videoInfo.title - 视频标题
        // videoInfo.videoUrl - 视频下载链接
        // videoInfo.cover - 视频封面
      } else {
        console.error('解析失败:', res.data.message);
      }
    },
    fail: (err) => {
      console.error('请求失败:', err);
    }
  });
}
```

---

**更新时间**: 2025-08-28
**版本**: v2.0.0 (支持安全认证)
