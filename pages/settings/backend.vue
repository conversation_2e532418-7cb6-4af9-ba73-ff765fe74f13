<template>
  <view class="backend-settings">
    <!-- 导航栏 -->
    <uni-nav-bar 
      title="后端设置" 
      left-icon="back" 
      @clickLeft="goBack"
      background-color="#007AFF"
      color="#FFFFFF"
    />
    
    <!-- 当前后端状态 -->
    <view class="status-section">
      <view class="status-card">
        <view class="status-header">
          <text class="status-title">当前后端</text>
          <view class="status-indicator" :class="{ 'online': isOnline, 'offline': !isOnline }">
            <text class="status-dot"></text>
            <text class="status-text">{{ isOnline ? '在线' : '离线' }}</text>
          </view>
        </view>
        <view class="status-info">
          <text class="backend-name">{{ currentBackendInfo.name }}</text>
          <text class="backend-type">{{ currentBackendInfo.type }}</text>
        </view>
        <button class="test-btn" @click="testConnection" :loading="testing">
          {{ testing ? '检测中...' : '连接测试' }}
        </button>
      </view>
    </view>
    
    <!-- 后端选择 -->
    <view class="section">
      <view class="section-title">选择后端类型</view>
      <view class="backend-options">
        <view 
          class="backend-option" 
          :class="{ 'active': currentBackend === 'unicloud' }"
          @click="switchBackend('unicloud')"
        >
          <view class="option-icon">☁️</view>
          <view class="option-content">
            <text class="option-title">uniCloud</text>
            <text class="option-desc">使用uniCloud云函数，免费稳定</text>
          </view>
          <view class="option-check" v-if="currentBackend === 'unicloud'">✓</view>
        </view>
        
        <view 
          class="backend-option" 
          :class="{ 'active': currentBackend === 'springboot' }"
          @click="switchBackend('springboot')"
        >
          <view class="option-icon">🌱</view>
          <view class="option-content">
            <text class="option-title">SpringBoot</text>
            <text class="option-desc">使用SpringBoot REST API，功能丰富</text>
          </view>
          <view class="option-check" v-if="currentBackend === 'springboot'">✓</view>
        </view>
      </view>
    </view>
    
    <!-- SpringBoot配置 -->
    <view class="section" v-if="currentBackend === 'springboot'">
      <view class="section-title">SpringBoot配置</view>
      
      <!-- 环境选择 -->
      <view class="config-item">
        <text class="config-label">运行环境</text>
        <picker 
          :value="springbootEnvIndex" 
          :range="springbootEnvOptions" 
          range-key="name"
          @change="onSpringbootEnvChange"
        >
          <view class="picker-display">
            {{ springbootEnvOptions[springbootEnvIndex].name }}
            <text class="picker-arrow">></text>
          </view>
        </picker>
      </view>
      
      <!-- 服务器地址 -->
      <view class="config-item">
        <text class="config-label">服务器地址</text>
        <text class="config-value">{{ springbootConfig.baseUrl }}</text>
      </view>
      
      <!-- API Key状态 -->
      <view class="config-item">
        <text class="config-label">API Key</text>
        <view class="api-key-status">
          <text class="api-key-text">{{ hasApiKey ? '已配置' : '未配置' }}</text>
          <button class="api-key-btn" @click="refreshApiKey" :loading="refreshingApiKey">
            {{ refreshingApiKey ? '刷新中...' : '刷新' }}
          </button>
        </view>
      </view>
    </view>
    
    <!-- 高级设置 -->
    <view class="section">
      <view class="section-title">高级设置</view>
      
      <view class="config-item">
        <text class="config-label">请求超时</text>
        <text class="config-value">{{ timeout / 1000 }}秒</text>
      </view>
      
      <view class="config-item">
        <text class="config-label">支持平台</text>
        <text class="config-value">{{ supportedPlatforms.length }}个平台</text>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="actions">
      <button class="action-btn secondary" @click="resetConfig">重置配置</button>
      <button class="action-btn primary" @click="saveConfig">保存设置</button>
    </view>
  </view>
</template>

<script>
import apiService from '../../utils/api-service.js'

export default {
  data() {
    return {
      // 当前状态
      currentBackend: 'unicloud',
      currentBackendInfo: {},
      isOnline: false,
      testing: false,
      
      // SpringBoot配置
      springbootConfig: {},
      springbootEnvIndex: 0,
      springbootEnvOptions: [
        { value: 'development', name: '开发环境 (localhost)' },
        { value: 'production', name: '生产环境 (云服务器)' }
      ],
      hasApiKey: false,
      refreshingApiKey: false,
      
      // 其他配置
      timeout: 30000,
      supportedPlatforms: []
    }
  },
  
  onLoad() {
    this.loadCurrentConfig()
    this.testConnection()
    this.loadSupportedPlatforms()
  },
  
  methods: {
    /**
     * 加载当前配置
     */
    loadCurrentConfig() {
      const backendInfo = apiService.getCurrentBackendInfo()
      this.currentBackendInfo = backendInfo
      this.currentBackend = backendInfo.type
      
      if (backendInfo.type === 'springboot') {
        this.springbootConfig = backendInfo.config.server[backendInfo.config.config.currentEnv]
        this.springbootEnvIndex = backendInfo.config.config.currentEnv === 'development' ? 0 : 1
        this.checkApiKey()
      }
      
      this.timeout = backendInfo.config.timeout || 30000
    },
    
    /**
     * 切换后端类型
     */
    async switchBackend(backendType) {
      if (this.currentBackend === backendType) return
      
      try {
        uni.showLoading({ title: '切换中...' })
        
        apiService.switchBackend(backendType)
        this.currentBackend = backendType
        this.loadCurrentConfig()
        
        // 重新测试连接
        await this.testConnection()
        
        uni.hideLoading()
        uni.showToast({
          title: `已切换到${backendType === 'unicloud' ? 'uniCloud' : 'SpringBoot'}`,
          icon: 'success'
        })
      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: `切换失败: ${error.message}`,
          icon: 'error'
        })
      }
    },
    
    /**
     * 切换SpringBoot环境
     */
    onSpringbootEnvChange(e) {
      const index = e.detail.value
      const env = this.springbootEnvOptions[index].value
      
      try {
        apiService.switchSpringBootEnv(env)
        this.springbootEnvIndex = index
        this.loadCurrentConfig()
        
        uni.showToast({
          title: `已切换到${this.springbootEnvOptions[index].name}`,
          icon: 'success'
        })
      } catch (error) {
        uni.showToast({
          title: `切换失败: ${error.message}`,
          icon: 'error'
        })
      }
    },
    
    /**
     * 测试连接
     */
    async testConnection() {
      this.testing = true
      try {
        this.isOnline = await apiService.healthCheck()
      } catch (error) {
        this.isOnline = false
      }
      this.testing = false
    },
    
    /**
     * 检查API Key状态
     */
    checkApiKey() {
      if (this.currentBackend === 'springboot') {
        const apiKey = uni.getStorageSync('springboot_api_key')
        this.hasApiKey = !!apiKey
      }
    },
    
    /**
     * 刷新API Key
     */
    async refreshApiKey() {
      this.refreshingApiKey = true
      try {
        // 清除旧的API Key
        apiService.backendConfig.clearSpringBootApiKey()
        
        // 获取新的API Key
        await apiService.backendConfig.getSpringBootApiKey()
        
        this.hasApiKey = true
        uni.showToast({
          title: 'API Key刷新成功',
          icon: 'success'
        })
      } catch (error) {
        uni.showToast({
          title: `刷新失败: ${error.message}`,
          icon: 'error'
        })
      }
      this.refreshingApiKey = false
    },
    
    /**
     * 加载支持的平台
     */
    async loadSupportedPlatforms() {
      try {
        this.supportedPlatforms = await apiService.getSupportedPlatforms()
      } catch (error) {
        console.error('加载平台列表失败:', error)
      }
    },
    
    /**
     * 重置配置
     */
    resetConfig() {
      uni.showModal({
        title: '确认重置',
        content: '这将清除所有后端配置，确定要重置吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除本地存储
            uni.removeStorageSync('backend_type')
            uni.removeStorageSync('springboot_env')
            uni.removeStorageSync('springboot_api_key')
            
            // 重新加载配置
            this.loadCurrentConfig()
            
            uni.showToast({
              title: '配置已重置',
              icon: 'success'
            })
          }
        }
      })
    },
    
    /**
     * 保存设置
     */
    saveConfig() {
      uni.showToast({
        title: '设置已保存',
        icon: 'success'
      })
    },
    
    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack()
    }
  }
}
</script>

<style scoped>
.backend-settings {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.status-section {
  padding: 20rpx;
}

.status-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #ccc;
}

.status-indicator.online .status-dot {
  background: #52c41a;
}

.status-indicator.offline .status-dot {
  background: #ff4d4f;
}

.status-text {
  font-size: 24rpx;
  color: #666;
}

.status-info {
  margin-bottom: 30rpx;
}

.backend-name {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.backend-type {
  font-size: 28rpx;
  color: #666;
}

.test-btn {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.section {
  margin: 20rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
}

.section-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}

.backend-options {
  padding: 20rpx;
}

.backend-option {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s;
}

.backend-option.active {
  border-color: #007AFF;
  background: #f0f8ff;
}

.option-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.option-content {
  flex: 1;
}

.option-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.option-desc {
  font-size: 26rpx;
  color: #666;
}

.option-check {
  font-size: 36rpx;
  color: #007AFF;
  font-weight: bold;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.config-item:last-child {
  border-bottom: none;
}

.config-label {
  font-size: 30rpx;
  color: #333;
}

.config-value {
  font-size: 28rpx;
  color: #666;
}

.picker-display {
  display: flex;
  align-items: center;
  gap: 10rpx;
  font-size: 28rpx;
  color: #666;
}

.picker-arrow {
  color: #ccc;
}

.api-key-status {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.api-key-text {
  font-size: 28rpx;
  color: #666;
}

.api-key-btn {
  background: #007AFF;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.actions {
  padding: 40rpx 20rpx;
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  padding: 30rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  border: none;
}

.action-btn.primary {
  background: #007AFF;
  color: white;
}

.action-btn.secondary {
  background: #f0f0f0;
  color: #666;
}
</style>
