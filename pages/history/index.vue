<template>
  <view class="container">
    <!-- 顶部广告位 -->
    <view class="top-ad-placeholder" v-if="shouldShowTopAd">
      <view class="ad-header">
        <text class="ad-label">📺 广告</text>
      </view>
      <view class="ad-content">
        <view class="ad-thumbnail">
          <text class="ad-icon">📚</text>
        </view>
        <view class="ad-info">
          <text class="ad-title">历史记录管理</text>
          <text class="ad-desc">智能分类，快速查找解析记录</text>
          <view class="ad-source">
            <text class="ad-source-icon">📊</text>
            <text class="ad-source-text">墨影去水印</text>
          </view>
        </view>
        <button class="ad-action-btn" @click="onTopAdClick">查看详情</button>
      </view>
    </view>

    <!-- 顶部操作栏 -->
    <view class="header">
      <text class="title">历史记录</text>
      <view class="header-actions">
        <button class="delete-all-btn" @click="clearAllHistory" v-if="historyList.length > 0">
          <text class="delete-icon">🗑️</text>
          <text class="delete-all-text">全部删除</text>
        </button>
      </view>
    </view>

    <!-- 记录数量提示 -->
    <view class="record-limit-tips" v-if="historyList.length > 0">
      <text class="limit-text">📝 最多保存100条记录，超出会自动覆盖最早的记录</text>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="historyList.length === 0">
      <text class="empty-icon">🕒</text>
      <text class="empty-text">暂无历史记录</text>
      <text class="empty-tip">解析视频、图片或文本内容后会显示在这里</text>
      <button class="goto-parse-btn" @click="goToParse">
        <text class="goto-icon">🚀</text>
        <text class="goto-text">去解析内容</text>
      </button>
    </view>

    <!-- 历史记录列表 -->
    <scroll-view scroll-y class="history-list" v-else>
            <view class="history-item" v-for="item in historyList" :key="item.id">
        <!-- 链接展示和时间 -->
        <view class="item-header">
          <view class="item-time">{{ formatTime(item.timestamp) }}</view>
          <view class="item-actions">
            <text class="action-btn delete-btn" @click="deleteItem(item.id)" title="删除记录">🗑️</text>
          </view>
        </view>

        <!-- 链接内容 -->
        <view class="link-content" v-if="item.originalLink" @click="copyText(item.originalLink)">
          <text class="link-text">{{ item.originalLink }}</text>
          <text class="copy-hint">点击复制</text>
        </view>

        <!-- 底部操作按钮 -->
        <view class="item-footer">
          <button class="action-button copy-btn" @click="copyText(item.originalLink)">
            <text class="btn-icon">📋</text>
            <text class="btn-text">复制链接</text>
          </button>
          <button class="action-button reprocess-btn" @click="reprocess(item)">
            <text class="btn-icon">🔄</text>
            <text class="btn-text">重新解析</text>
          </button>
        </view>
      </view>
    </scroll-view>

    <!-- 底部统计信息 -->
    <view class="footer" v-if="historyList.length > 0">
      <text class="stats">共 {{ historyList.length }} 条记录，最多保存 100 条</text>
    </view>
  </view>
</template>

<script>
import adManager from '../../components/ad-config.js'

export default {
  data() {
    return {
      historyList: []
    }
  },
  
  computed: {
    // 🔧 检查是否显示历史页广告
    shouldShowTopAd() {
      return adManager.shouldShowAd('result') // 复用result广告类型
    }
  },
  
  onShow() {
    this.loadHistory()
  },
  
  methods: {
    // 加载历史记录
    loadHistory() {
      const history = uni.getStorageSync('watermark_history') || []
      this.historyList = history.sort((a, b) => b.timestamp - a.timestamp)
    },
    
    // 顶部广告关闭（现在通过配置控制，不再需要手动关闭）
    closeTopAd() {
      uni.showToast({
        title: '广告控制已集中管理',
        icon: 'none'
      })
      console.log('[广告] 历史页广告现在通过全局配置控制，不支持单独关闭')
    },
    
    // 顶部广告点击
    onTopAdClick() {
      console.log('历史页顶部广告被点击')
      // 这里可以添加广告点击统计或其他逻辑
    },
    
    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const now = new Date()
      const diff = now - date
      
      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前'
      } else if (diff < 86400000) { // 24小时内
        return Math.floor(diff / 3600000) + '小时前'
      } else if (diff < 604800000) { // 7天内
        return Math.floor(diff / 86400000) + '天前'
      } else {
        return date.toLocaleDateString()
      }
    },
    
    // 格式化URL显示
    formatUrl(url) {
      if (!url) return ''
      
      // 显示域名和简化的路径
      try {
        const urlObj = new URL(url)
        const domain = urlObj.hostname
        const path = urlObj.pathname
        
        if (path.length > 20) {
          return `${domain}${path.substring(0, 20)}...`
        }
        return `${domain}${path}`
      } catch (e) {
        // 如果URL解析失败，显示前30个字符
        return url.length > 30 ? url.substring(0, 30) + '...' : url
      }
    },
    
    // 获取平台友好名称
    getPlatformText(platform) {
      const platformMap = {
        'douyin': 'DY',
        'kuaishou': 'KS',
        'xiaohongshu': '小红薯',
        'bilibili': 'BL站',
        'weibo': '围脚',
        'weishi': 'WS',
        'pipix': 'PPX',
        'qishui': 'QS音乐',
        'zuiyou': 'ZY'
      }
      return platformMap[platform] || '未知平台'
    },
    
    // 获取类型徽章样式
    getTypeBadgeClass(platform) {
      return `badge-${platform}`
    },
    
    // 检查是否有URL信息（现在只检查原始链接）
    hasUrls(item) {
      return item.originalLink
    },
    
    // 复制文本
    copyText(text) {
      if (!text) return
      
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: '已复制',
            icon: 'success',
            duration: 1500
          })
        }
      })
    },
    
    // 复制图片URLs
    copyImageUrls(imageUrls) {
      if (!imageUrls || imageUrls.length === 0) return
      
      const urlsText = imageUrls.join('\n')
      this.copyText(urlsText)
    },
    
    // 复制全部信息（安全模式：只包含基本信息和原始链接）
    copyAllInfo(item) {
      let allInfo = `标题：${item.title}\n`
      
      if (item.author) {
        allInfo += `作者：${item.author}\n`
      }
      
      allInfo += `来源：${item.source}\n`
      allInfo += `类型：${item.contentType}\n`
      
      if (item.description) {
        allInfo += `描述：${item.description}\n`
      }
      
      if (item.originalLink) {
        allInfo += `原始链接：${item.originalLink}\n`
      }
      
      allInfo += `记录时间：${new Date(item.timestamp).toLocaleString()}\n`
      allInfo += `\n注意：如需获取解析内容，请重新解析此链接`
      
      this.copyText(allInfo)
    },
    
    // 重新解析
    reprocess(item) {
      // 跳转到主页面并传递数据
      uni.switchTab({
        url: '/pages/watermark-remover/index'
      })
      
      // 通过事件总线传递数据
      uni.$emit('reprocessItem', item)
    },
    
    // 删除单个记录
    deleteItem(id) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这条记录吗？',
        success: (res) => {
          if (res.confirm) {
            const history = uni.getStorageSync('watermark_history') || []
            const newHistory = history.filter(item => item.id !== id)
            uni.setStorageSync('watermark_history', newHistory)
            this.loadHistory()
            
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
          }
        }
      })
    },
    
    // 清空所有历史记录
    clearAllHistory() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空所有历史记录吗？此操作不可恢复。',
        success: (res) => {
          if (res.confirm) {
            uni.removeStorageSync('watermark_history')
            this.historyList = []
            
            uni.showToast({
              title: '清空成功',
              icon: 'success'
            })
          }
        }
      })
    },
    
    // 跳转到解析页面
    goToParse() {
      uni.switchTab({
        url: '/pages/watermark-remover/index'
      })
    }
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #FFF1F0 0%, #FFEBE8 50%, #FFF1F0 100%);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 40rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #E5E5E5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.delete-all-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: rgba(255, 59, 48, 0.1);
  border: 1rpx solid rgba(255, 59, 48, 0.2);
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
  height: 56rpx;
  transition: all 0.3s ease;
}

.delete-all-btn:active {
  background: rgba(255, 59, 48, 0.2);
  transform: scale(0.95);
}

.delete-icon {
  font-size: 20rpx;
}

.delete-all-text {
  font-size: 24rpx;
  color: #FF3B30;
  font-weight: 500;
}

/* 记录数量提示样式 */
.record-limit-tips {
  background: rgba(255, 193, 7, 0.08);
  margin: 0 20rpx 20rpx;
  border-radius: 12rpx;
  padding: 16rpx;
  border: 1rpx solid rgba(255, 193, 7, 0.2);
}

.limit-text {
  font-size: 24rpx;
  color: #FF8F00;
  text-align: center;
  line-height: 1.4;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 150rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #CCCCCC;
  text-align: center;
  margin-bottom: 60rpx;
}

.goto-parse-btn {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 20rpx 40rpx;
  background: linear-gradient(45deg, #E1251B, #FF4142);
  color: #FFFFFF;
  border: none;
  border-radius: 50rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.goto-icon {
  font-size: 24rpx;
}

.history-list {
  flex: 1;
  padding: 20rpx;
}

.history-item {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #F0F0F0;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.item-main-info {
  flex: 1;
  padding-right: 20rpx;
}

.item-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
  line-height: 1.4;
  cursor: pointer;
}

.item-title:active {
  color: #E1251B;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.item-time {
  font-size: 24rpx;
  color: #999;
}

.item-type-badge {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #FFFFFF;
}

.badge-douyin { background: #FF0050; }
.badge-kuaishou { background: #FF6600; }
.badge-xiaohongshu { background: #FF2442; }
.badge-bilibili { background: #00A1D6; }
.badge-weibo { background: #E6162D; }
.badge-weishi { background: #FFD700; color: #333; }
.badge-pipix { background: #FF1744; }
.badge-qishui { background: #1DB584; }
.badge-unknown { background: #999999; }

.badge-text {
  font-size: 20rpx;
  font-weight: 500;
}

.item-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.action-btn {
  font-size: 32rpx;
  padding: 10rpx;
  border-radius: 50%;
  min-width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reprocess-btn {
  background: rgba(225, 37, 27, 0.1);
  color: #E1251B;
}

.delete-btn {
  background: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

.reprocess-btn:active {
  background: rgba(0, 122, 255, 0.2);
}

.delete-btn:active {
  background: rgba(255, 59, 48, 0.2);
}

.item-content {
  margin-bottom: 24rpx;
}

.content-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
}

.content-label {
  font-size: 26rpx;
  color: #666;
  min-width: 80rpx;
  flex-shrink: 0;
}

.content-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  line-height: 1.4;
}

.content-value:active {
  color: #E1251B;
}

.description-text {
  line-height: 1.5;
}

.url-section {
  background: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

.section-title {
  font-size: 24rpx;
  color: #666;
  font-weight: 600;
  display: block;
  margin-bottom: 16rpx;
}

.url-item {
  display: flex;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #EEEEEE;
  transition: background 0.2s ease;
}

.url-item:last-child {
  border-bottom: none;
}

.url-item:active {
  background: rgba(0, 122, 255, 0.05);
  border-radius: 8rpx;
}

.url-item.special:active {
  background: rgba(255, 193, 7, 0.1);
}

.url-label {
  font-size: 24rpx;
  color: #666;
  min-width: 80rpx;
  flex-shrink: 0;
}

.url-value {
  font-size: 24rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
  line-height: 1.3;
}

.copy-hint {
  font-size: 20rpx;
  color: #E1251B;
  margin-left: 12rpx;
}

.live-badge {
  background: linear-gradient(45deg, #FF6B35, #F7931E);
  color: #FFFFFF;
  font-size: 18rpx;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
  font-weight: 600;
  margin-left: 12rpx;
}

.security-notice {
  background: rgba(255, 193, 7, 0.1);
  border: 1rpx solid rgba(255, 193, 7, 0.3);
  border-radius: 8rpx;
  padding: 16rpx;
  margin-top: 16rpx;
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
}

.notice-icon {
  font-size: 24rpx;
  color: #FF8F00;
  flex-shrink: 0;
}

.notice-text {
  font-size: 22rpx;
  color: #E65100;
  line-height: 1.4;
  flex: 1;
}

.link-section {
  margin-bottom: 20rpx;
}

.link-item {
  background: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  transition: background 0.2s ease;
}

.link-item:active {
  background: #E9ECEF;
}

.link-label {
  font-size: 24rpx;
  color: #666;
  min-width: 120rpx;
  flex-shrink: 0;
}

.link-value {
  font-size: 24rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
  line-height: 1.3;
  margin-right: 12rpx;
}

.copy-icon {
  font-size: 20rpx;
  color: #E1251B;
}

/* 新的简化样式 */
.link-content {
  background: #F8F9FA;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border: 1rpx solid #E9ECEF;
  transition: all 0.2s ease;
}

.link-content:active {
  background: #E9ECEF;
  transform: scale(0.99);
}

.link-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.4;
  word-break: break-all;
  display: block;
  margin-bottom: 8rpx;
}

.copy-hint {
  font-size: 22rpx;
  color: #E1251B;
  text-align: right;
  display: block;
}

.action-button {
  flex: 1;
  height: 70rpx;
  border: none;
  border-radius: 35rpx;
  font-size: 26rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  margin: 0 8rpx;
  transition: all 0.3s ease;
}

.action-button:active {
  transform: scale(0.95);
}

.copy-btn {
  background: linear-gradient(45deg, #E1251B, #FF4142);
  color: #FFFFFF;
}

.reprocess-btn {
  background: linear-gradient(45deg, #34C759, #30D158);
  color: #FFFFFF;
}

.btn-icon {
  font-size: 24rpx;
}

.btn-text {
  font-size: 26rpx;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1rpx solid #F0F0F0;
}

.footer-info {
  flex: 1;
}

.platform-info {
  font-size: 24rpx;
  color: #999;
}

.quick-copy-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: rgba(0, 122, 255, 0.1);
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #E1251B;
  font-weight: 500;
}

.quick-copy-btn:active {
  background: rgba(0, 122, 255, 0.2);
}

.btn-icon {
  font-size: 20rpx;
}

.btn-text {
  font-size: 24rpx;
}

.footer {
  padding: 30rpx 40rpx;
  background: #ffffff;
  border-top: 1rpx solid #E5E5E5;
  text-align: center;
}

.stats {
  font-size: 24rpx;
  color: #999;
}

/* 滚动优化 */
.history-list {
  scroll-behavior: smooth;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16rpx;
  }
  
  .item-actions {
    align-self: flex-end;
  }
}

/* 顶部广告位样式 */
.top-ad-placeholder {
  background: #FFFFFF;
  margin: 20rpx 0;  /* 左右无空白，上下有间距 */
  border-radius: 16rpx;  /* 圆角设计 */
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid #E0E0E0;
  width: 100%;  /* 确保宽度一致 */
  box-sizing: border-box;
}

.top-ad-placeholder .ad-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.top-ad-placeholder .ad-label {
  font-size: 20rpx;
  color: #999;
  background: #F5F5F5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.top-ad-placeholder .ad-close {
  font-size: 24rpx;
  color: #999;
  cursor: pointer;
  padding: 4rpx;
}

.top-ad-placeholder .ad-content {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.top-ad-placeholder .ad-thumbnail {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #FF6B6B 0%, #FF8E53 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.top-ad-placeholder .ad-icon {
  font-size: 32rpx;
  color: #FFFFFF;
}

.top-ad-placeholder .ad-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.top-ad-placeholder .ad-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.top-ad-placeholder .ad-desc {
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

.top-ad-placeholder .ad-source {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.top-ad-placeholder .ad-source-icon {
  font-size: 20rpx;
  color: #999;
}

.top-ad-placeholder .ad-source-text {
  font-size: 20rpx;
  color: #999;
}

.top-ad-placeholder .ad-action-btn {
  background: linear-gradient(45deg, #007AFF, #5856D6);
  color: #FFFFFF;
  border: none;
  border-radius: 20rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  font-weight: 500;
  cursor: pointer;
  flex-shrink: 0;
}

/* 广告位容器统一样式 */
.top-ad-placeholder {
  margin-left: 0;
  margin-right: 0;
  padding-left: 24rpx;
  padding-right: 24rpx;
}
</style>