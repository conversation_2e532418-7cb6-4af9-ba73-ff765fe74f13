<template>
  <view class="container">
    <!-- 用户信息区域 -->
    <view class="user-section">
      <view class="user-info">
        <!-- 固定的用户头像 -->
        <view class="avatar">
          <image class="avatar-image" src="/static/moying-logo.jpg" mode="aspectFill"></image>
        </view>
        <view class="user-details">
          <text class="username" @click="editNickname">{{ userInfo.nickName }}</text>
          <text class="user-desc">欢迎使用墨影去水印~</text>
        </view>
      </view>
      
      <!-- 用户统计 -->
      <view class="stats-section">
        <view class="stat-item">
          <text class="stat-number">{{ historyCount }}</text>
          <text class="stat-label">解析记录</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-number">{{ usageDays }}</text>
          <text class="stat-label">使用天数</text>
        </view>
      </view>
    </view>

    <!-- 个人中心广告 -->
    <ProfileAd @adClick="onProfileAdClick" @adClose="onProfileAdClose" />

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group">
        <view class="menu-item" @click="goToHistory">
          <view class="menu-icon">📋</view>
          <text class="menu-title">解析记录</text>
          <text class="menu-desc">查看历史解析记录</text>
          <text class="menu-arrow">›</text>
        </view>
        
        <view class="menu-item" @click="goToSettings">
          <view class="menu-icon">💬</view>
          <text class="menu-title">联系客服</text>
          <text class="menu-desc">获得技术支持和帮助</text>
          <text class="menu-arrow">›</text>
        </view>
        <view class="menu-item" @click="showTutorial">
          <view class="menu-icon">📖</view>
          <text class="menu-title">使用教程</text>
          <text class="menu-desc">如何使用去水印功能</text>
          <text class="menu-arrow">›</text>
        </view>


      </view>

      <view class="menu-group">
        

        <button 
          v-if="shouldShowShareButton" 
          class="menu-item share-menu-item official-share-btn" 
          open-type="share"
          
        >
          <view class="menu-icon">📤</view>
          <text class="menu-title">推荐给好友</text>
          <text class="menu-desc">{{ shareMenuDesc }}</text>
          <text class="menu-arrow">›</text>
        </button>
      </view>
    </view>

    <!-- 底部信息 -->
    <view class="footer-info">
      <text class="app-version">版本 v{{ appVersion }}</text>
      <text class="copyright">© 2025 墨影去水印</text>
      </view>
  </view>
</template>

<script>
import ProfileAd from '../../components/profile-ad/profile-ad.vue'
import shareManager from '../../components/share-config.js'
import appConfig from '../../components/app-config.js'

export default {
  components: {
    ProfileAd
  },
  
  data() {
    return {
      historyCount: 0,
      usageDays: 1,
      userInfo: {
        nickName: '微信用户' // 默认昵称，用户可自定义
      },
      // 应用信息
      appVersion: appConfig.getConfig().app.version
    }
  },

  computed: {
    // 判断是否应该显示分享按钮
    shouldShowShareButton() {
      return shareManager.shouldShowShare('profilePage')
    },

    // 分享菜单描述
    shareMenuDesc() {
      return '去水印一键搞定，分享高清视频乐趣'
    }
  },

  onShow() {
    this.loadUserStats()
    this.loadUserNickname()
  },

  onHide() {
    // 页面隐藏时，通知分享弹窗关闭
    uni.$emit('onHide')
  },
  
  methods: {
    // 微信官方分享回调
    onShareAppMessage() {
      console.log('[分享] 个人中心页面分享回调')
      
      // 在返回分享配置时给权限
      const result = shareManager.handleOfficialShare({})
      
      // 返回分享配置
      return {
        title: 'MarkEraser - 短视频去水印工具',
        desc: '免费去除抖音、快手、小红书等平台视频水印',
        path: '/pages/watermark-remover/index',
        imageUrl: '/static/share-card.jpg'
      }
    },

    // 加载用户统计数据
    loadUserStats() {
      // 获取历史记录数量
      const history = uni.getStorageSync('watermark_history') || []
      this.historyCount = history.length

      // 计算使用天数
      const firstUseTime = uni.getStorageSync('first_use_time')
      if (firstUseTime) {
        const daysDiff = Math.floor((Date.now() - firstUseTime) / (1000 * 60 * 60 * 24))
        this.usageDays = Math.max(1, daysDiff)
      } else {
        // 如果是首次使用，记录当前时间
        uni.setStorageSync('first_use_time', Date.now())
        this.usageDays = 1
      }

      // 获取分享统计数据
      this.shareStats = shareManager.getShareStats()
    },
    
    // 跳转到历史记录页面
    goToHistory() {
      uni.switchTab({
        url: '/pages/history/index'
      })
    },
    
    // 跳转到设置页面
    goToSettings() {
      uni.navigateTo({
        url: '/pages/settings/index'
      })
    },
    
    // 显示使用教程
    showTutorial() {
      uni.navigateTo({
        url: '/pages/tutorial/index'
      })
    },


    
    // 个人中心广告事件处理
    onProfileAdClick(event) {
      console.log('个人中心广告被点击:', event)
    },

    onProfileAdClose(event) {
      console.log('个人中心广告被关闭:', event)
    },

    // 编辑昵称
    editNickname() {
      uni.showModal({
        title: '设置昵称',
        editable: true,
        placeholderText: '请输入您的昵称',
        content: this.userInfo.nickName === '微信用户' ? '' : this.userInfo.nickName,
        success: (res) => {
          if (res.confirm && res.content) {
            const newNickname = res.content.trim()
            if (newNickname && newNickname.length <= 20) {
              this.userInfo.nickName = newNickname
              this.saveUserNickname()
              uni.showToast({
                title: '昵称设置成功',
                icon: 'success'
              })
            } else if (newNickname.length > 20) {
              uni.showToast({
                title: '昵称太长，请不要超过20个字符',
                icon: 'none'
              })
            }
          }
        }
      })
    },
    
    // 加载用户昵称
    loadUserNickname() {
      const savedNickname = uni.getStorageSync('user_nickname')
      if (savedNickname) {
        this.userInfo.nickName = savedNickname
      }
    },
    
    // 保存用户昵称
    saveUserNickname() {
      uni.setStorageSync('user_nickname', this.userInfo.nickName)
      console.log('💾 [昵称] 已保存用户昵称:', this.userInfo.nickName)
    }




  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background: #F5F7FA;
}

.user-section {
  background: linear-gradient(135deg, #E1251B 0%, #FF4142 50%, #E1251B 100%);
  padding: 60rpx 40rpx 40rpx;
  color: #ffffff;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 40rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.2);
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.avatar-image {
  width: 100%;
  height: 100%;
}

.user-details {
  flex: 1;
}

.username {
  font-size: 36rpx;
  font-weight: 600;
  display: block;
  margin-bottom: 12rpx;
  cursor: pointer;
  padding: 8rpx 12rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.username:active {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.username::after {
  content: '✏️';
  font-size: 24rpx;
  margin-left: 8rpx;
  opacity: 0.7;
}

.user-desc {
  font-size: 26rpx;
  opacity: 0.8;
}

.avatar-button {
  padding: 0;
  border: none;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.avatar-button::after {
  border: none;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.avatar-edit-hint {
  position: absolute;
  bottom: -5rpx;
  right: -5rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.avatar-hint {
  font-size: 22rpx;
  opacity: 0.6;
  margin-top: 8rpx;
  display: block;
}

.nickname-section {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
}

.username {
  font-size: 36rpx;
  font-weight: 600;
  cursor: pointer;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.username:active {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.edit-hint {
  font-size: 22rpx;
  opacity: 0.7;
  color: rgba(255, 255, 255, 0.8);
}

.user-details {
  flex: 1;
}

.user-desc {
  font-size: 26rpx;
  opacity: 0.8;
}

.stats-section {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 30rpx 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.stat-number {
  font-size: 32rpx;
  font-weight: 700;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

.stat-divider {
  width: 1rpx;
  height: 40rpx;
  background: rgba(255, 255, 255, 0.3);
}

.menu-section {
  padding: 20rpx;
}

.menu-group {
  background: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #F0F0F0;
  transition: background 0.2s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background: #F8F9FA;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 24rpx;
  width: 60rpx;
  text-align: center;
}

.menu-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-right: 16rpx;
}

.menu-desc {
  font-size: 24rpx;
  color: #999;
  flex: 1;
}

.menu-arrow {
  font-size: 32rpx;
  color: #C7C7CC;
  font-weight: bold;
}

.share-menu-item {
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 165, 0, 0.1));
  border: 1rpx solid rgba(255, 107, 107, 0.2);
}

/* 官方分享按钮样式 */
.official-share-btn {
  /* 重置button默认样式 */
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  outline: none;
  
  /* 继承菜单项样式 */
  display: flex;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 20rpx;
  background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 165, 0, 0.1));
  border: 1rpx solid rgba(255, 107, 107, 0.2);
  border-radius: 16rpx;
  position: relative;
  overflow: hidden;
}

.official-share-btn::after {
  /* 移除button默认的边框 */
  border: none;
}

.share-menu-item .menu-icon {
  background: linear-gradient(135deg, #ff6b6b, #ffa500);
  color: #ffffff;
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.share-menu-item .menu-desc {
  color: #ff6b6b;
  font-weight: 500;
}

.footer-info {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

.app-version {
  font-size: 24rpx;
  display: block;
  margin-bottom: 8rpx;
}

.copyright {
  font-size: 22rpx;
  opacity: 0.7;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .user-info {
    flex-direction: column;
    text-align: center;
    gap: 16rpx;
  }
  
  .stats-section {
    margin-top: 30rpx;
  }
  
  .menu-item {
    flex-wrap: wrap;
    gap: 8rpx;
  }
  
  .menu-desc {
    width: 100%;
    margin-top: 8rpx;
    padding-left: 84rpx;
  }
}

/* 动画效果 */
.menu-item {
  transition: all 0.3s ease;
}

.menu-item:hover {
  transform: translateX(4rpx);
}

/* 渐变动画 */
.user-section {
  background: linear-gradient(-45deg, #E1251B, #FF4142, #E1251B, #FF4142);
  background-size: 400% 400%;
  animation: gradientShift 10s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50% }
  50% { background-position: 100% 50% }
  100% { background-position: 0% 50% }
}


</style>