/**
 * 分享功能配置管理 - 激励分享模式
 * 用于统一管理分享功能的开关、奖励机制和配置参数
 * 设计理念：完全可插拔，不强制分享，提供额外奖励激励用户主动分享
 */

// 📝 分享功能配置 - 激励分享模式
const SHARE_CONFIG = {
  // ============== 基本开关 ==============
  globalEnabled: true,                     // 🔧 总开关：是否启用分享功能
                                          // ❌ false时，所有分享入口都不会显示

  // ============== 官方按钮配置 ==============
  useOfficialButton: true,                 // 🔧 使用官方分享按钮
                                          // ✅ true时，所有分享都通过官方button组件实现
                                          // ❌ false时，使用自定义分享弹窗（已弃用）

  // ============== 分享权限配置 ==============
  sharePermission: {
    enabled: true,                        // ✅ 启用分享权限
    duration: 24 * 60 * 60 * 1000,      // ⏰ 权限时长：24小时
    description: '分享后获得更流畅的使用体验'  // 🎁 委婉的描述
  },
  
  // ============== 分享入口配置 ==============
  shareEntries: {
    resultPage: {
      enabled: true,                      // ✅ 结果页面分享按钮
      position: 'bottom',                 // 📍 位置：bottom/top/floating
      showRewardHint: true,               // 💡 显示奖励提示
      description: '解析结果页面的分享按钮'
    },
    profilePage: {
      enabled: true,                      // ✅ 个人中心分享推广
      showRewardHint: true,               // 💡 显示奖励提示
      description: '个人中心页面的分享推广'
    },
    floatingButton: {
      enabled: false,                     // ❌ 悬浮分享按钮（可选）
      position: 'right-bottom',           // 📍 位置：right-bottom/left-bottom
      description: '全局悬浮分享按钮'
    }
  },
  
  // ============== 分享方式配置 ==============
  shareTypes: {
    wechatFriend: {
      enabled: true,                      // ✅ 微信好友分享
      title: 'MarkEraser - 短视频去水印工具',
      description: '免费去除抖音、快手、小红书等平台视频水印',
      imageUrl: '/static/logo.png'        // 分享图片
    },
    wechatMoments: {
      enabled: true,                      // ✅ 朋友圈分享
      title: 'MarkEraser - 短视频去水印工具',
      description: '免费去除抖音、快手、小红书等平台视频水印',
      imageUrl: '/static/logo.png'
    },
    copyLink: {
      enabled: true,                      // ✅ 复制链接分享
      linkTemplate: '推荐一个好用的短视频去水印工具：MarkEraser，支持抖音、快手、小红书等多个平台！',
      description: '复制分享链接到其他平台'
    }
  },
  
  // ============== 统计配置 ==============
  statistics: {
    enabled: true,                        // ✅ 启用分享统计
    trackShareCount: true,                // 📊 统计分享次数
    trackRewardCount: true,               // 🎁 统计奖励发放次数
    trackConversion: true,                // 📈 统计转化率
    description: '分享功能使用统计'
  }
}

// 📱 分享管理器 - 激励分享模式
class ShareManager {
  constructor() {
    this.config = SHARE_CONFIG
    this.shareStates = {
      lastShareTime: 0,                   // 上次分享时间
      totalShares: 0                      // 总分享次数
    }
    
    // 加载本地存储的状态
    this.loadShareStates()
  }
  
  // 🔄 处理官方分享按钮（适用于所有官方分享场景）
  handleOfficialShare(shareData) {
    console.log('[分享管理器] 官方分享按钮回调')
    
    // 在onShareAppMessage中给权限，这是用户确实触发了分享配置
    console.log('[分享管理器] 用户触发分享配置，授予24小时权限')
    this.grantSharePermission()
    
    return {
      success: true,
      shareType: 'wechatFriend',
      timestamp: Date.now()
    }
  }
  
  // 🔍 检查是否应该显示分享功能
  shouldShowShare(entryType) {
    if (!this.config.globalEnabled) return false

    const entryConfig = this.config.shareEntries[entryType]
    return entryConfig && entryConfig.enabled
  }
  
  // 📋 获取分享配置
  getShareConfig(entryType) {
    return this.config.shareEntries[entryType] || {}
  }
  
  // 🎁 检查是否可以获得分享奖励
  canGetShareReward() {
    if (!this.config.globalEnabled || !this.config.sharePermission.enabled) {
      return { allowed: false, reason: '分享功能已禁用' }
    }

    return { allowed: true, reason: this.config.sharePermission.description }
  }
  
  // ✅ 处理分享成功
  handleShareSuccess(shareResult) {
    console.log('[分享] 分享成功', shareResult)

    // 直接设置24小时权限
    const now = Date.now()
    uni.setStorageSync('unlimited_access_time', now)
    console.log('[分享] 分享成功，设置24小时权限:', now)

    // 更新分享统计
    this.updateShareStats(shareResult.shareType)

    // 显示成功提示
    uni.showToast({
      title: '感谢分享支持！',
      icon: 'success',
      duration: 2000
    })
  }
  


  // 🔍 检查当前权限状态（与广告系统保持一致）
  hasUnlimitedAccess() {
    const unlimitedTime = uni.getStorageSync('unlimited_access_time')
    if (!unlimitedTime) {
      return false
    }

    const now = Date.now()
    const timeElapsed = now - unlimitedTime
    const duration = 24 * 60 * 60 * 1000 // 24小时

    // 检查是否在24小时权限期内
    if (timeElapsed < duration) {
      return true
    }

    // 24小时权限已过期，清理存储
    uni.removeStorageSync('unlimited_access_time')
    return false
  }

  // 📊 获取权限剩余时间
  getRemainingAccessTime() {
    const unlimitedTime = uni.getStorageSync('unlimited_access_time')
    if (!unlimitedTime) {
      return 0
    }

    const now = Date.now()
    const remainingTime = (unlimitedTime + 24 * 60 * 60 * 1000) - now

    return Math.max(0, remainingTime)
  }
  
  // 🎁 授予分享权限（激励分享奖励）
  grantSharePermission() {
    const now = Date.now()
    uni.setStorageSync('unlimited_access_time', now)
    console.log('[分享管理器] 感谢您的分享支持！获得更流畅体验至:', new Date(now + 24 * 60 * 60 * 1000).toLocaleString())
    
    // 触发权限获得事件
    uni.$emit('shareRewardGranted', {
      grantTime: now,
      expireTime: now + 24 * 60 * 60 * 1000
    })
  }
  
  //  更新分享统计
  updateShareStats(shareType) {
    const now = Date.now()
    
    this.shareStates.lastShareTime = now
    this.shareStates.totalShares++

    this.saveShareStates()

    console.log(`[分享] 统计已更新: ${shareType}, 总分享次数: ${this.shareStates.totalShares}`)
  }
  
  // 💾 保存分享状态
  saveShareStates() {
    uni.setStorageSync('share_states', this.shareStates)
  }
  
  // 📖 加载分享状态
  loadShareStates() {
    const savedStates = uni.getStorageSync('share_states')
    if (savedStates) {
      this.shareStates = { ...this.shareStates, ...savedStates }
    }
    
    // 检查是否需要重置每日计数
    this.checkDailyReset()
  }
  
  // 🔄 检查每日重置
  checkDailyReset() {
    const now = Date.now()
    const lastResetDate = uni.getStorageSync('share_last_reset_date') || 0
    const today = new Date(now).toDateString()
    const lastResetDateStr = new Date(lastResetDate).toDateString()
    
    if (today !== lastResetDateStr) {
      // 重置每日计数
      this.shareStates.dailyShareCount = 0
      this.shareStates.dailyRewardCount = 0
      uni.setStorageSync('share_last_reset_date', now)
      this.saveShareStates()
      console.log('[分享] 每日计数已重置')
    }
  }
  
  // 📊 获取分享统计
  getShareStats() {
    return {
      ...this.shareStates,
      canGetPermission: this.canGetSharePermission().allowed
    }
  }
  
  // 🗑️ 清理分享数据（调试用）
  clearShareData() {
    uni.removeStorageSync('share_states')
    uni.removeStorageSync('share_last_reset_date')
    // 清理分享统计数据
    const shareTypes = ['wechatFriend', 'wechatMoments', 'copyLink']
    shareTypes.forEach(type => {
      uni.removeStorageSync(`share_stats_${type}`)
    })

    this.shareStates = {
      lastShareTime: 0,
      totalShares: 0
    }
    console.log('[分享] 分享数据已清理')
  }

  // 🔄 重置所有分享状态（调试用）
  resetAllShareStates() {
    this.clearShareData()
    // 注意：不清理 unlimited_access_time，因为这是与广告系统共享的权限
    console.log('[分享] 所有分享状态已重置')
  }

  // 📊 获取详细的分享统计报告
  getDetailedShareStats() {
    const stats = this.getShareStats()
    const shareTypes = ['wechatFriend', 'wechatMoments', 'copyLink']
    const typeStats = {}

    shareTypes.forEach(type => {
      const typeData = uni.getStorageSync(`share_stats_${type}`) || { count: 0, lastTime: 0 }
      typeStats[type] = typeData
    })

    return {
      ...stats,
      typeBreakdown: typeStats,
      remainingAccessTime: this.getRemainingAccessTime(),
      hasCurrentAccess: this.hasUnlimitedAccess()
    }
  }

  // 🎯 检查分享功能是否可用
  isShareFeatureAvailable() {
    return this.config.globalEnabled
  }

  // ⚙️ 更新配置
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig }
    console.log('[分享] 配置已更新', newConfig)
  }

  // 🔧 获取分享按钮配置（供UI组件使用）
  getShareButtonConfig(entryType) {
    if (!this.shouldShowShare(entryType)) {
      return { show: false }
    }

    const entryConfig = this.getShareConfig(entryType)
    const rewardCheck = this.canGetShareReward()

    return {
      show: true,
      showRewardHint: entryConfig.showRewardHint && rewardCheck.allowed,
      rewardText: rewardCheck.allowed ? '分享给好友' : rewardCheck.reason,
      position: entryConfig.position || 'bottom'
    }
  }

  // 🐛 调试方法：检查权限状态
  debugPermissionStatus() {
    const unlimitedTime = uni.getStorageSync('unlimited_access_time')
    const hasAccess = this.hasUnlimitedAccess()
    const remainingTime = this.getRemainingAccessTime()

    console.log('=== 权限状态调试 ===')
    console.log('unlimited_access_time:', unlimitedTime)
    console.log('hasUnlimitedAccess():', hasAccess)
    console.log('剩余时间(小时):', remainingTime / (60 * 60 * 1000))
    console.log('当前时间:', Date.now())

    if (unlimitedTime) {
      console.log('权限设置时间:', new Date(unlimitedTime).toLocaleString())
      console.log('权限过期时间:', new Date(unlimitedTime + 24 * 60 * 60 * 1000).toLocaleString())
    }

    return { unlimitedTime, hasAccess, remainingTime }
  }
}

// 创建全局分享管理器实例
const shareManager = new ShareManager()

export default shareManager
export { SHARE_CONFIG }
