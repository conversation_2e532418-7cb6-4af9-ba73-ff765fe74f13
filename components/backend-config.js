/**
 * 后端配置文件
 * 开发者在此配置后端类型和服务器地址
 * 用户无法在前端修改这些配置
 */

const backendConfig = {
  // ============== 后端类型配置 ==============
  // 当前使用的后端类型: 'unicloud' | 'springboot'
  // 开发者修改此处来切换后端
  currentBackend: 'unicloud',
  
  // ============== uniCloud配置 ==============
  unicloud: {
    enabled: true,
    name: 'uniCloud云函数',
    description: '使用uniCloud云函数作为后端',
    // uniCloud不需要配置服务器地址，使用云函数调用
    functions: {
      parser: 'unified-parser',        // 统一解析器云函数
      downloader: 'file-downloader'    // 文件下载器云函数
    }
  },
  
  // ============== SpringBoot配置 ==============
  springboot: {
    enabled: true,
    name: 'SpringBoot服务',
    description: '使用SpringBoot REST API作为后端',
    
    // 服务器配置 - 开发者在此配置SpringBoot服务器地址
    server: {
      // 开发环境配置 (本地测试)
      development: {
        baseUrl: 'http://localhost:8080',
        apiPrefix: '/api/v1',
        timeout: 30000,
        description: '本地开发服务器'
      },

      // 生产环境配置 (云服务器)
      production: {
        baseUrl: 'https://your-domain.com',  // 修改为你的云服务器域名
        apiPrefix: '/api/v1',
        timeout: 30000,
        description: '生产环境服务器'
      }
    },

    // 当前使用的环境: 'development' | 'production'
    // 开发者修改此处来切换开发/生产环境
    currentEnv: 'development',
    
    // API认证配置
    auth: {
      enabled: true,
      masterKey: 'ClearMark-Master-2025-SecureKey-9527',  // 主密钥，用于生成API Key
      apiKey: '',  // 运行时生成的API Key，存储在本地
      storageKey: 'springboot_api_key'  // 本地存储键名
    },
    
    // API端点配置
    endpoints: {
      // 认证相关
      generateKey: '/auth/generate-key',
      
      // 解析相关
      parse: '/parser/parse',
      detect: '/parser/detect', 
      platforms: '/parser/platforms',
      health: '/parser/health'
    }
  },
  
  // ============== 通用配置 ==============
  common: {
    // 请求超时时间
    timeout: 30000,
    
    // 重试配置
    retry: {
      enabled: true,
      maxAttempts: 3,
      delay: 1000
    },
    
    // 缓存配置
    cache: {
      enabled: true,
      duration: 5 * 60 * 1000  // 5分钟缓存
    }
  }
}

/**
 * 获取当前后端配置
 */
function getCurrentBackendConfig() {
  const backend = backendConfig.currentBackend
  
  if (backend === 'unicloud') {
    return {
      type: 'unicloud',
      config: backendConfig.unicloud,
      timeout: backendConfig.common.timeout
    }
  } else if (backend === 'springboot') {
    const springbootConfig = backendConfig.springboot
    const envConfig = springbootConfig.server[springbootConfig.currentEnv]
    
    return {
      type: 'springboot',
      config: springbootConfig,
      server: envConfig,
      baseUrl: envConfig.baseUrl + envConfig.apiPrefix,
      timeout: envConfig.timeout || backendConfig.common.timeout
    }
  }
  
  throw new Error(`不支持的后端类型: ${backend}`)
}



/**
 * 获取SpringBoot API Key
 */
async function getSpringBootApiKey() {
  const config = backendConfig.springboot
  
  // 先尝试从本地存储获取
  let apiKey = uni.getStorageSync(config.auth.storageKey)
  if (apiKey) {
    console.log('✅ 使用缓存的API Key')
    return apiKey
  }
  
  // 如果没有，则生成新的API Key
  console.log('🔑 生成新的API Key...')
  try {
    const envConfig = config.server[config.currentEnv]
    const response = await uni.request({
      url: envConfig.baseUrl + envConfig.apiPrefix + config.endpoints.generateKey,
      method: 'POST',
      header: {
        'X-Master-Key': config.auth.masterKey
      },
      timeout: envConfig.timeout
    })
    
    if (response[1].data.success) {
      apiKey = response[1].data.data.apiKey
      // 保存到本地存储
      uni.setStorageSync(config.auth.storageKey, apiKey)
      console.log('✅ API Key生成成功')
      return apiKey
    } else {
      throw new Error(response[1].data.message || 'API Key生成失败')
    }
  } catch (error) {
    console.error('❌ API Key生成失败:', error)
    throw error
  }
}

/**
 * 清除SpringBoot API Key
 */
function clearSpringBootApiKey() {
  const config = backendConfig.springboot
  uni.removeStorageSync(config.auth.storageKey)
  console.log('🗑️ 已清除API Key缓存')
}

/**
 * 初始化后端配置
 */
function initBackendConfig() {
  console.log('🚀 后端配置初始化完成:', {
    当前后端: backendConfig.currentBackend,
    SpringBoot环境: backendConfig.springboot.currentEnv,
    配置来源: '开发者配置文件'
  })
}

// 导出配置和方法
export default {
  // 配置对象
  config: backendConfig,

  // 核心方法
  getCurrentBackendConfig,

  // SpringBoot相关方法
  getSpringBootApiKey,
  clearSpringBootApiKey,

  // 初始化方法
  init: initBackendConfig,

  // 便捷方法
  isUniCloud: () => backendConfig.currentBackend === 'unicloud',
  isSpringBoot: () => backendConfig.currentBackend === 'springboot',

  // 获取当前后端名称
  getCurrentBackendName: () => {
    const backend = backendConfig.currentBackend
    if (backend === 'unicloud') return backendConfig.unicloud.name
    if (backend === 'springboot') return backendConfig.springboot.name
    return '未知后端'
  }
}
