/**
 * 应用配置文件
 * 统一管理应用名称、版本等基础信息
 */

const appConfig = {
  // 应用基础信息
  app: {
    // 当前应用名称
    name: '墨影去水印',
    
    // 应用描述
    description: '专业去除视频水印，支持多平台',
    slogan: '支持主流短视频平台，永久免费使用',
    
    // 版本信息
    version: '1.1.0',
    buildNumber: '1'
  },
  
  // 支持的平台
  supportedPlatforms: [
    { name: 'DY', icon: '🎵', key: 'douyin' },
    { name: 'K<PERSON>', icon: '⚡', key: 'kuaishou' },
    { name: '小红薯', icon: '📖', key: 'xiaoh<PERSON>shu' },
    { name: 'BL站', icon: '📺', key: 'bilibili' },
    { name: '围脚', icon: '🐦', key: 'weibo' },
    { name: 'WS', icon: '📱', key: 'weishi' },
    { name: 'PPX', icon: '🦐', key: 'pipix' },
    { name: 'QS音乐', icon: '🎵', key: 'qishui' },
    { name: 'ZY', icon: '👈', key: 'zuiyou' }
  ],
  
  // 功能特性
  features: [
    '无水印视频下载',
    '多平台支持',
    '高清画质保持',
    '快速解析',
    '历史记录管理',
    '图片视频混合下载',
    '实时预览',
    '一键保存到相册'
  ]
}

/**
 * 获取当前应用名称
 */
function getAppName() {
  return appConfig.app.name
}

/**
 * 获取应用描述
 */
function getAppDescription() {
  return appConfig.app.description
}

/**
 * 获取应用标语
 */
function getAppSlogan() {
  return appConfig.app.slogan
}

/**
 * 获取支持的平台列表
 */
function getSupportedPlatforms() {
  return appConfig.supportedPlatforms
}



/**
 * 更新应用名称
 * @param {string} newName 新的应用名称
 */
function updateAppName(newName) {
  appConfig.app.name = newName
  console.log(`[应用配置] 应用名称已更新为: ${newName}`)
}

/**
 * 获取支持平台数量
 */
function getSupportedPlatformCount() {
  return appConfig.supportedPlatforms.length
}

/**
 * 获取完整配置
 */
function getConfig() {
  return appConfig
}

export default {
  getAppName,
  getAppDescription,
  getAppSlogan,
  getSupportedPlatforms,
  getSupportedPlatformCount,
  updateAppName,
  getConfig
}
