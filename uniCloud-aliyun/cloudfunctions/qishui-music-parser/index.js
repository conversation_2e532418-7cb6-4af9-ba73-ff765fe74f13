'use strict';

/**
 * 汽水音乐解析器
 * 支持汽水音乐视频链接解析和下载
 */

// 全局调试配置
let DEBUG = false;

exports.main = async (event, context) => {
  const { link, debug = false } = event || {};
  DEBUG = !!debug;

  if (!link || typeof link !== 'string') {
    return { success: false, message: '链接不能为空' };
  }

  try {
    if (DEBUG) console.log('🔵 开始解析汽水音乐链接:', link);
    
    // 清理链接
    const cleanedLink = cleanQishuiUrl(link);
    if (DEBUG) console.log('🔄 清理后的链接:', cleanedLink);

    // 解析汽水音乐内容
    const result = await parseQishuiContent(cleanedLink);
    
    if (result.success) {
      if (DEBUG) {
        console.log('🎉 汽水音乐解析成功!');
        console.log('📄 内容信息:', {
          标题: result.data.title?.substring(0, 30) + '...',
          作者: result.data.author,
          时长: result.data.duration + '秒'
        });
      }
      return result;
    } else {
      if (DEBUG) console.log('❌ 汽水音乐解析失败:', result.message);
      return result;
    }
    
  } catch (error) {
    console.error('💥 汽水音乐解析异常:', error.message);
    return { success: false, message: error.message };
  }
};

/**
 * 清理汽水音乐链接
 */
function cleanQishuiUrl(url) {
  if (!url || typeof url !== 'string') return '';
  
  try {
    url = url.trim();
    
    // 处理汽水音乐链接，移除查询参数
    if (url.includes('qishui.douyin.com')) {
      return url.split('?')[0];
    }
    
    return url;
  } catch (error) {
    return url;
  }
}

/**
 * 解析汽水音乐内容
 */
async function parseQishuiContent(url) {
  try {
    if (DEBUG) console.log('🌐 获取汽水音乐页面HTML');

    // 获取HTML内容
    const htmlResult = await getQishuiHtml(url);
    if (!htmlResult.success) {
      return { success: false, message: htmlResult.message };
    }

    if (DEBUG) console.log(`✅ HTML获取成功，长度: ${htmlResult.html.length}`);

    // 从HTML中提取JSON数据
    const jsonData = extractJsonFromHtml(htmlResult.html);
    if (!jsonData) {
      return { success: false, message: '未找到有效的汽水音乐数据' };
    }

    if (DEBUG) console.log('✅ JSON数据提取成功');

    // 解析视频信息
    return parseVideoContent(jsonData);

  } catch (error) {
    return { success: false, message: error.message };
  }
}

/**
 * 获取汽水音乐页面HTML
 */
async function getQishuiHtml(url) {
  try {
    if (DEBUG) console.log('🌐 开始获取页面HTML');

    // 手动处理重定向
    let finalUrl = url;
    let redirectCount = 0;
    const maxRedirects = 5;
    
    while (redirectCount < maxRedirects) {
      const response = await uniCloud.httpclient.request(finalUrl, {
        method: 'GET',
        timeout: 20000,
        dataType: 'text',
        followRedirect: false,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate',  // 不支持Brotli压缩
          'Referer': 'https://music.douyin.com/'
        }
      });
      
      // 处理重定向
      if (response.status >= 300 && response.status < 400) {
        const location = response.headers.location || response.headers.Location;
        if (location) {
          finalUrl = location.startsWith('http') ? location : new URL(location, finalUrl).href;
          redirectCount++;
          if (DEBUG) console.log(`🔄 重定向到: ${finalUrl}`);
          continue;
        }
      }
      
      // 成功获取内容
      if (response.status === 200 && response.data) {
        let htmlContent = response.data;
        
        // 处理响应数据类型
        if (typeof htmlContent === 'string') {
          // 已经是字符串
        } else if (Buffer.isBuffer(htmlContent)) {
          htmlContent = htmlContent.toString('utf8');
        } else {
          htmlContent = String(htmlContent);
        }
        
        if (DEBUG) console.log('✅ 页面获取成功');
        return { success: true, html: htmlContent };
      }
      
      return { success: false, message: `HTTP ${response.status}` };
    }
    
    return { success: false, message: `重定向次数过多(>${maxRedirects})` };
    
  } catch (error) {
    return { success: false, message: error.message };
  }
}

/**
 * 从HTML中提取JSON数据
 */
function extractJsonFromHtml(html) {
  if (DEBUG) console.log('🔍 提取JSON数据');

  try {
    // 提取 _ROUTER_DATA（汽水音乐的主要数据源）
    const routerDataMatch = html.match(/_ROUTER_DATA\s*=\s*({[\s\S]*?});/);
    if (routerDataMatch) {
      try {
        const routerData = JSON.parse(routerDataMatch[1]);
        
        // 检查是否包含视频信息
        if (routerData.loaderData && routerData.loaderData.ugc_video_page) {
          if (DEBUG) console.log('✅ 找到视频信息数据');
          return { source: 'router_data', data: routerData };
        }
      } catch (error) {
        if (DEBUG) console.log('❌ _ROUTER_DATA解析失败:', error.message);
      }
    }
    
    if (DEBUG) console.log('❌ 未找到有效数据');
    return null;
    
  } catch (error) {
    if (DEBUG) console.log('❌ JSON提取失败:', error.message);
    return null;
  }
}

/**
 * 解析视频内容
 */
function parseVideoContent(extractedData) {
  try {
    if (DEBUG) console.log('🎬 解析视频内容');

    const routerData = extractedData.data;
    const videoInfo = routerData.loaderData.ugc_video_page;
    
    if (!videoInfo) {
      return { success: false, message: '未找到视频信息数据' };
    }
    
    // 提取视频信息
    const videoOptions = videoInfo.videoOptions || {};
    const title = videoOptions.videoName || videoOptions.title || '';
    const author = videoOptions.artistName || '';
    const videoUrl = videoOptions.url || '';
    const coverUrl = videoOptions.coverURL || videoOptions.metaURL || '';
    const duration = videoOptions.duration || 0;
    const width = videoOptions.width || 1920;
    const height = videoOptions.height || 1080;
    
    if (DEBUG) {
      console.log('📝 解析结果:');
      console.log(`- 标题: ${title}`);
      console.log(`- 作者: ${author}`);
      console.log(`- 时长: ${duration}秒`);
    }
    
    // 构建返回结果
    const result = {
      success: true,
      data: {
        type: 'video',
        title: title,
        author: author,
        description: `来自汽水音乐的视频: ${title}`,
        createTime: Date.now(),
        platform: 'qishui',
        source: '汽水音乐',
        originalUrl: videoUrl,
        
        // 视频信息
        videoUrl: videoUrl,
        coverUrl: coverUrl,
        duration: Math.round(duration),
        width: width,
        height: height,
        
        // processedData结构
        processedData: {
          data: videoUrl,
          isUrl: true,
          type: 'video/mp4',
          duration: Math.round(duration),
          videoUrls: [videoUrl],
          imageUrls: []
        },

        note: '汽水音乐视频解析成功'
      }
    };

    if (DEBUG) console.log('✅ 视频解析完成');
    return result;

  } catch (error) {
    if (DEBUG) console.log('❌ 视频解析失败:', error.message);
    return { success: false, message: `视频解析失败: ${error.message}` };
  }
}