'use strict';

/**
 * 好看视频解析器
 * 支持解析好看视频的视频信息和下载链接
 */

const DEBUG = true;

exports.main = async (event, context) => {
  const { link, debug = DEBUG } = event;

  if (!link) {
    return {
      success: false,
      message: '链接不能为空'
    };
  }

  try {
    // 检测是否为好看视频链接
    if (!isHaokanLink(link)) {
      return {
        success: false,
        message: '仅支持好看视频链接'
      };
    }

    if (debug) console.log('开始解析好看视频链接:', link);

    // 解析好看视频内容
    const result = await parseHaokanContent(link, debug);

    return {
      success: true,
      data: result,
      timestamp: new Date().toISOString(),
      version: "好看视频解析器 v1.0"
    };

  } catch (error) {
    console.error('好看视频解析失败:', error);
    return {
      success: false,
      message: error.message || '解析失败',
      error: debug ? error.stack : undefined
    };
  }
};

// 检测是否为好看视频链接
function isHaokanLink(link) {
  return /haokan\.baidu\.com/i.test(link);
}

// 解析好看视频内容
async function parseHaokanContent(link, debug = true) {
  if (debug) console.log('开始解析好看视频链接:', link);
  
  // 获取HTML页面数据
  const htmlData = await fetchHaokanHtml(link, debug);
  
  // 从HTML中提取JSON数据
  const jsonData = extractJsonFromHtml(htmlData, debug);
  
  // 解析JSON数据
  const result = parseJsonData(jsonData, link, debug);
  
  // 设置原始链接
  result.originalUrl = link;
  
  return result;
}

// 获取好看视频HTML页面
async function fetchHaokanHtml(link, debug = true) {
  // 优先使用移动端请求头（因为移动端能获取到完整的主视频数据）
  const mobileHeaders = {
    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache'
  };

  try {
    if (debug) console.log('尝试移动端请求头获取页面:', link);

    const response = await uniCloud.httpclient.request(link, {
      method: 'GET',
      timeout: 15000,
      dataType: 'text',
      followRedirect: true,
      headers: mobileHeaders
    });

    if (response.status === 200 && response.data) {
      if (debug) console.log('移动端请求成功，页面大小:', response.data.length);

      // 检查是否包含有效的JSON数据
      if (response.data.includes('__PRELOADED_STATE__')) {
        if (debug) console.log('✅ 使用移动端请求头获取数据成功');
        return response.data;
      }
    }

    if (debug) console.log('移动端请求无效数据，尝试PC端请求头');

  } catch (error) {
    if (debug) console.log('移动端请求失败，尝试PC端请求头:', error.message);
  }

  // 使用PC端请求头作为备用
  const pcHeaders = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache'
  };

  try {
    if (debug) console.log('使用PC端请求头获取页面');

    const response = await uniCloud.httpclient.request(link, {
      method: 'GET',
      timeout: 15000,
      dataType: 'text',
      followRedirect: true,
      headers: pcHeaders
    });

    if (response.status === 200 && response.data) {
      if (debug) console.log('PC端请求成功，页面大小:', response.data.length);
      if (debug) console.log('✅ 使用PC端请求头获取数据成功');
      return response.data;
    }

    throw new Error('获取页面失败');
  } catch (error) {
    console.error('获取页面失败:', error);
    throw new Error('获取页面失败: ' + error.message);
  }
}

// 从HTML中提取JSON数据
function extractJsonFromHtml(html, debug = true) {
  try {
    if (debug) console.log('开始从HTML中提取JSON数据');
    
    // 提取 window.__PRELOADED_STATE__ 数据
    const pattern = /window\.__PRELOADED_STATE__\s*=\s*({[\s\S]*?});\s*document\.querySelector/;
    const match = html.match(pattern);

    if (match && match[1]) {
      const jsonData = JSON.parse(match[1]);
      if (debug) {
        console.log('✅ 成功提取JSON数据');
        console.log('📄 ===== 好看视频完整JSON数据 =====');
        console.log(jsonData);
        console.log('📄 ===== JSON数据结束 =====');
      }
      return jsonData;
    }
    
    throw new Error('未找到JSON数据');
  } catch (error) {
    console.error('JSON提取失败:', error);
    throw new Error('JSON数据提取失败: ' + error.message);
  }
}

// 解析JSON数据
function parseJsonData(jsonData, link, debug = true) {
  try {
    if (debug) console.log('开始解析JSON数据');

    // 从URL中提取目标视频ID
    const urlParams = new URLSearchParams(link.split('?')[1]);
    const targetVid = urlParams.get('vid');
    if (debug) console.log('目标视频ID:', targetVid);

    let videoMeta = null;

    // 优先尝试从curVideoMeta获取数据
    if (jsonData.curVideoMeta) {
      videoMeta = jsonData.curVideoMeta;
      if (debug) console.log('✅ 从curVideoMeta获取数据');
    }
    if (!videoMeta) {
      if (debug) console.log('❌ 未找到视频元数据，可用字段:', Object.keys(jsonData));
      throw new Error('未找到视频元数据');
    }

    // 提取基本信息 - 适配PC端数据结构
    const title = videoMeta.title || '';
    const author = videoMeta.mth?.author_name || videoMeta.source_name || '';
    const description = videoMeta.description || '';
    const coverUrl = videoMeta.poster || videoMeta.poster_big || '';

    // 时长处理 - PC端是字符串格式 "03:43"
    let duration = 0;
    if (videoMeta.time_length) {
      const timeParts = videoMeta.time_length.split(':');
      if (timeParts.length === 2) {
        duration = parseInt(timeParts[0]) * 60 + parseInt(timeParts[1]);
      }
    } else if (videoMeta.duration) {
      duration = videoMeta.duration;
    }

    const playCount = videoMeta.playcnt || 0;
    const publishTime = videoMeta.publish_time || 0;

    // 提取视频链接 - 适配PC端数据结构
    let videoUrls = [];

    // PC端数据结构中，视频链接在 playurl 字段
    if (videoMeta.playurl) {
      videoUrls.push({
        quality: '标清',
        url: convertToHttps(videoMeta.playurl),
        size: 0,
        bitrate: 0,
        rank: 0
      });
      if (debug) console.log('✅ 从playurl获取视频链接');
    }

    // 尝试从 clarityUrl 数组中提取不同清晰度的视频链接（移动端结构）
    if (videoMeta.clarityUrl && Array.isArray(videoMeta.clarityUrl)) {
      videoUrls = videoMeta.clarityUrl.map(item => ({
        quality: item.title || item.key || 'unknown',
        url: convertToHttps(item.url || ''),
        size: item.videoSize || 0,
        bitrate: item.videoBps || 0,
        rank: item.rank || 0
      })).filter(item => item.url)
        .sort((a, b) => b.rank - a.rank);  // 按rank降序排序（高清晰度优先）
      if (debug) console.log('✅ 从clarityUrl获取多清晰度视频链接');
    }

    if (debug) {
      console.log('解析结果:');
      console.log('标题:', title);
      console.log('作者:', author);
      console.log('视频链接数量:', videoUrls.length);
      console.log('清晰度排序 (高到低):', videoUrls.map(v => `${v.quality}(${v.bitrate}kbps)`).join(' > '));
    }

    // 构建标准化返回结果 - 与其他平台保持一致的格式
    const result = {
      title: title,
      author: author,
      content: description,
      coverUrl: coverUrl,
      type: 'video',
      platform: 'haokan',
      source: '好看视频',
      duration: duration,
      playCount: playCount,
      publishTime: publishTime,
      processedData: {
        data: videoUrls.length > 0 ? videoUrls[0].url : '', // 主视频URL（最高清晰度）
        isUrl: true,
        type: 'video/mp4',
        duration: duration,
        videoUrls: videoUrls.map(v => v.url), // 统一为URL数组格式
        imageUrls: [], // 好看视频主要是视频内容
        hasDirectUrl: true,
        requiresProxy: false
      }
    };

    return result;

  } catch (error) {
    console.error('JSON解析失败:', error);
    throw new Error('JSON数据解析失败: ' + error.message);
  }
}



// 转换为HTTPS链接
function convertToHttps(url) {
  if (!url) return '';
  return url.replace(/^http:\/\//i, 'https://');
}
