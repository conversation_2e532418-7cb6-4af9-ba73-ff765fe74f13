'use strict';

/**
 * 简化版抖音解析器 - 用于快速验证
 * 使用Node.js实现，避免Python环境配置问题
 */


exports.main = async (event, context) => {
  console.log('收到解析请求:', event);

  const { link, forceRemoveWatermark = false, debug = false, getCover = false } = event;

  if (!link) {
    return {
      success: false,
      message: '链接不能为空'
    };
  }

  try {
    // 检测是否为抖音链接
    if (!isDouyinLink(link)) {
      // 提供更详细的错误信息
      if (link.includes('douyin') && !link.includes('douyin.com')) {
        return {
          success: false,
          message: '链接不完整，请检查是否完整复制了抖音链接'
        };
      }
      return {
        success: false,
        message: '仅支持抖音链接'
      };
    }

    // 如果只是获取封面，使用主解析流程然后提取封面信息
    if (getCover) {
      const result = await parseDouyinVideo(link);
      return {
        success: true,
        data: {
          title: result.title,
          author: result.author,
          coverUrl: result.coverUrl,
          type: 'cover',
          platform: 'douyin',
          source: '抖音'
        }
      };
    }


    // 使用自建解析
    console.log('使用标准解析方法');
    
    // 清理链接
    let cleanedLink = cleanLink(link);
    console.log('清理后的链接:', cleanedLink);
    
    const result = await parseDouyinVideo(cleanedLink, forceRemoveWatermark);

    return {
      success: true,
      data: result
    };

  } catch (error) {
    console.error('解析失败:', error);
    return {
      success: false,
      message: error.message || '解析失败'
    };
  }
};

function isDouyinLink(link) {
  return /douyin\.com|dy\.com/i.test(link);
}

// 递归查找对象中的desc字段
// 通用递归查找函数，合并desc/nickname/duration查找逻辑
function findFieldInObject(obj, targetField, alternativeFields = [], depth = 0) {
  if (!obj || typeof obj !== 'object' || depth > 8) {
    return null;
  }
  
  // 检查目标字段
  if (obj[targetField] && typeof obj[targetField] === 'string' && obj[targetField].trim()) {
    return obj[targetField];
  }
  
  // 检查备选字段
  for (const field of alternativeFields) {
    if (obj[field] && typeof obj[field] === 'string' && obj[field].trim()) {
      return obj[field];
    }
  }
  
  // 递归查找
  for (const key in obj) {
    if (obj.hasOwnProperty(key) && obj[key] && typeof obj[key] === 'object') {
      if (Array.isArray(obj[key])) {
        for (let i = 0; i < Math.min(obj[key].length, 3); i++) {
          const result = findFieldInObject(obj[key][i], targetField, alternativeFields, depth + 1);
          if (result) return result;
        }
      } else {
        const result = findFieldInObject(obj[key], targetField, alternativeFields, depth + 1);
        if (result) return result;
      }
    }
  }
  
  return null;
}

// 统一的标题和作者提取函数
function extractTitleAndAuthor(html) {
  // 优先从JSON提取
  const routerMatch = html.match(/window\._ROUTER_DATA\s*=\s*({.+?})\s*<\/script>/s);
  if (routerMatch) {
    try {
      const jsonData = JSON.parse(routerMatch[1]);
      
      // 🔍 打印从HTML中提取的完整原始JSON数据
      console.log('🎬 ===== 从HTML提取的抖音原始JSON数据 =====')
      console.log('📦 完整ROUTER_DATA JSON:', jsonData)
      console.log('========================')
      
      const title = findFieldInObject(jsonData, 'desc', ['title']) || '';
      const author = findFieldInObject(jsonData, 'nickname', ['author']) || '';
      if (title || author) {
        return { 
          title: title, 
          author: author 
        };
      }
    } catch (e) {
      console.error('JSON解析失败:', e)
      // JSON解析失败，继续下面的备用方案
    }
  }
  
  // 备用方案：从HTML title标签提取标题
  let title = '';
  const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
  if (titleMatch) {
    title = titleMatch[1].replace(/\s*-\s*抖音$/, '').trim();
  }
  
  // 备用方案：从正则匹配提取作者
  let author = '';
  const authorMatch = html.match(/"nickname":"([^"]+)"/);
  if (authorMatch) {
    author = authorMatch[1];
  }
  
  return { title, author };
}


// 基于官方API标准分析内容类型
function analyzeContentType(html) {
  try {
    // 开始内容类型分析

    // 1. 检查aweme_type
    const awemeTypeMatch = html.match(/"aweme_type":(\d+)/);
    const awemeType = awemeTypeMatch ? parseInt(awemeTypeMatch[1]) : null;
    console.log('aweme_type:', awemeType);

    // 2. 检查其他关键标识
    const hasImages = html.includes('"images":[') && !html.includes('"images":[]');
    const hasVideo = html.includes('"video":{') || html.includes('play_addr');
    const hasPlayAddr = html.includes('play_addr');
    
    console.log('关键标识检查:');
    console.log('- 包含images数组:', hasImages);
    console.log('- 包含video对象:', hasVideo);
    console.log('- 包含play_addr:', hasPlayAddr);

    // 3. 搜索更多线索
    const imageKeywords = ['"images"', 'image_list', 'photo'];
    const videoKeywords = ['"video"', 'play_addr', 'video_list'];
    
    let imageScore = 0;
    let videoScore = 0;
    
    imageKeywords.forEach(keyword => {
      if (html.includes(keyword)) {
        imageScore++;
      }
    });
    
    videoKeywords.forEach(keyword => {
      if (html.includes(keyword)) {
        videoScore++;
      }
    });
    
    console.log(`图片:${imageScore}, 视频:${videoScore}`);

    // 4. 基于aweme_type的官方判断（唯一判断标准）
    if (awemeType !== null) {
      // 图文内容类型
      if (awemeType === 2 || awemeType === 150) {
        console.log(`官方类型${awemeType} -> 图文内容`);
        return 'image';
      }
      // 视频内容类型
      else if (awemeType === 4 || awemeType === 68 || awemeType === 0 || awemeType === 1) {
        console.log(`官方类型${awemeType} -> 视频内容`);
        return 'video';
      }
      // 未知类型，默认为视频
      else {
        console.log(`未知的aweme_type: ${awemeType}，默认为视频`);
        return 'video';
      }
    }
    
    console.log('未找到aweme_type，默认为视频');
    return 'video';

  } catch (error) {
    console.error('内容类型分析失败:', error);
    return 'unknown';
  }
}

// 清理链接
function cleanLink(link) {
  // 移除多余的空格和换行符
  link = link.trim().replace(/\s+/g, ' ');
  
  // 提取URL
  const urlMatch = link.match(/(https?:\/\/[^\s]+)/);
  if (urlMatch) {
    let cleanedUrl = urlMatch[1];
    return cleanedUrl;
  }
  
  return link;
}



async function parseDouyinVideo(shareUrl) {
  console.log('开始解析抖音链接:', shareUrl);

  try {
    // 第一步：获取真实URL
    let realUrl;
    let isSlideLink = false;
    try {
      realUrl = await getRealUrl(shareUrl);
      console.log('真实URL:', realUrl);
      
      // 检查真实URL是否是slides链接
      if (realUrl.includes('/share/slides/')) {
        console.log('检测到真实URL是slides链接');
        isSlideLink = true;
        // 转换为note链接用于解析
        realUrl = realUrl.replace('/share/slides/', '/share/note/');
        console.log('转换后的真实URL:', realUrl);
      }
    } catch (urlError) {
      console.error('获取真实URL失败，使用原始URL:', urlError.message);
      realUrl = shareUrl;
    }

    // 第三步：获取页面内容，如果失败则使用备用方案
    let pageContent;
  
    try {
      pageContent = await getPageContent(realUrl);
      console.log('页面内容获取成功');
    } catch (pageError) {
      console.error('获取页面内容失败:', pageError.message);
      throw pageError;
    }

    // 分析内容类型
    const contentType = analyzeContentType(pageContent);

    let videoInfo;
    let isVideoContent;

    if (contentType === 'image') {
      console.log('确定为图文内容，使用图文解析...');
      videoInfo = await extractImageInfo(pageContent, shareUrl);
      isVideoContent = false;
    } else {
      // 默认按视频处理（包括 'video' 和 'unknown' 情况）
      console.log('按视频内容处理...');
      try {
        videoInfo = await extractVideoInfo(pageContent);
        isVideoContent = true;
      } catch (extractError) {
        console.error('从HTML提取视频信息失败:', extractError);

        throw extractError;
      }
    }

    // 第五步：根据内容类型处理
    let finalVideoUrl;
    if (isVideoContent) {
      // 对提取的视频URL进行去水印处理
      let processedUrl = await getNoWatermarkVideo(videoInfo.videoUrl);
      console.log('去水印处理后的URL:', processedUrl);

      finalVideoUrl = processedUrl;
    } else {
      finalVideoUrl = videoInfo.videoUrl; // 图文内容直接使用图片URL
    }

    // 第六步：根据内容类型处理数据
    let processedData;
    let note;
    let resultType;

    if (isVideoContent) {
      // 视频内容处理
      const durationInSeconds = Math.round(videoInfo.duration / 1000);
      const isDirectVideo = finalVideoUrl.includes('.mp4') || finalVideoUrl.includes('douyinvod.com');

      processedData = {
        data: finalVideoUrl,
        type: 'video/mp4',
        isUrl: true,
        duration: durationInSeconds,
        isDirectUrl: isDirectVideo
      };
      resultType = 'video';

      if (isDirectVideo) {
        note = '已获取无水印直链';
      } else {
        note = '已获取API链接';
      }
    } else {
      // 图文内容处理
      processedData = {
        data: videoInfo.videoUrl, // 主图片URL
        type: 'image/jpeg',
        isUrl: true,
        imageUrls: videoInfo.imageUrls, // 所有图片
        isImageContent: true
      };
      resultType = 'image';
      note = `已获取图文内容（共${videoInfo.imageUrls.length}张图片）`;
    }

    return {
      title: videoInfo.title,
      author: videoInfo.author,
      processedData: processedData,
      type: resultType, // 'video' 或 'image'
      platform: 'douyin',
      source: '抖音',
      note: note,
      coverUrl: videoInfo.coverUrl, // 添加封面URL
      originalUrl: shareUrl // 保留用户输入的原始分享链接
    };
    
  } catch (error) {
    console.error('抖音解析失败:', error);
    throw error;
  }
}

// 统一的请求headers配置
const DOUYIN_HEADERS = {
  'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1',
  'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
  'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
};

// 获取真实URL（处理重定向）
async function getRealUrl(shortUrl) {
  try {
    const response = await uniCloud.httpclient.request(shortUrl, {
      method: 'GET',
      followRedirect: false,
      timeout: 8000,
      headers: DOUYIN_HEADERS
    });

    const location = response.headers.location || response.headers.Location;
    return location || shortUrl;
  } catch (error) {
    console.error('获取真实URL失败:', error.message);
    return shortUrl;
  }
}

// 获取页面HTML内容
async function getPageContent(url) {
  console.log('获取页面内容:', url);
  
  try {
    const response = await uniCloud.httpclient.request(url, {
      method: 'GET',
      timeout: 10000,
      dataType: 'text',
      headers: {
        ...DOUYIN_HEADERS,
        'Referer': 'https://www.douyin.com/',
        'Cache-Control': 'no-cache'
      }
    });

    if (response.status !== 200) {
      throw new Error(`HTTP状态码错误: ${response.status}`);
    }

    let htmlContent = response.data;
    if (typeof htmlContent !== 'string') {
      htmlContent = htmlContent?.toString() || '';
      if (!htmlContent) {
        throw new Error('页面内容格式错误');
      }
    }

    if (!htmlContent || htmlContent.length < 100) {
      throw new Error('页面内容过短，可能加载失败');
    }

    return htmlContent;

  } catch (error) {
    console.error('获取页面内容失败:', error.message);
    throw new Error('无法获取页面内容: ' + error.message);
  }
}

async function extractVideoInfo(html) {
  try {
    if (typeof html !== 'string' || html.length < 100) {
      throw new Error('HTML内容无效');
    }

    // 提取标题和作者
    const { title, author } = extractTitleAndAuthor(html);

    // 视频URL提取 - 按优先级尝试
    let videoUrl = '';
    
    console.log('🎯 开始从JSON中提取视频URL...')
    
    const videoPatterns = [
      /"play_addr_h264"[^}]+?"url_list":\s*\[\s*"([^"]+)"/,  // 无水印
      /"play_addr"[^}]+?"url_list":\s*\[\s*"([^"]+)"/,       // 标准
      /"video"[^}]*?"play_addr"[^}]+?"url_list":\s*\[\s*"([^"]+)"/ // 图文视频
    ];
    
    const patternNames = ['无水印(play_addr_h264)', '标准(play_addr)', '图文视频'];
    
    for (let i = 0; i < videoPatterns.length; i++) {
      const pattern = videoPatterns[i];
      const match = html.match(pattern);
      console.log(`🔍 尝试模式 ${i + 1} [${patternNames[i]}]:`, match ? '找到' : '未找到');
      if (match) {
        videoUrl = match[1].replace(/\\u002F/g, '/').replace(/\\\//g, '/');
        console.log(`✅ 使用模式 ${i + 1} 提取到视频URL:`, videoUrl);
        break;
      }
    }
    
    if (!videoUrl) {
      throw new Error('未找到视频播放地址');
    }

    // 封面提取
    let coverUrl = '';
    const coverMatch = html.match(/"cover"[^}]*?"url_list":\s*\[\s*"([^"]+)"/);
    if (coverMatch) {
      coverUrl = fixIncompleteImageUrl(coverMatch[1]);
    }
    return { title, author, videoUrl, coverUrl };

  } catch (error) {
    console.error('提取视频信息失败:', error);
    throw error;
  }
}


// 提取图文信息（包括图文视频）
async function extractImageInfo(html, originalUrl = '') {
  try {
    // 使用统一的标题和作者提取函数
    const { title, author } = extractTitleAndAuthor(html);

    // 提取图片URL
    let imageUrls = [];
    
    // 先查找images数组的位置
    const imagesArrayIndex = html.indexOf('"images":[');
    if (imagesArrayIndex !== -1) {
      // 找到images数组的结束位置
      let bracketCount = 0;
      let startIndex = imagesArrayIndex + '"images":'.length;
      let endIndex = startIndex;

      for (let i = startIndex; i < html.length; i++) {
        if (html[i] === '[') {
          bracketCount++;
        } else if (html[i] === ']') {
          bracketCount--;
          if (bracketCount === 0) {
            endIndex = i;
            break;
          }
        }
      }

      if (endIndex > startIndex) {
        const imagesContent = html.substring(startIndex + 1, endIndex); // 去掉首尾的[]
        console.log('images数组内容长度:', imagesContent.length);
        console.log('images内容片段:', imagesContent.substring(0, 200));

        // 使用更简单的方法提取所有url_list
        const urlListPattern = /"url_list":\s*\[\s*"([^"]+)"/g;
        let match;
        let urlCount = 0;

        while ((match = urlListPattern.exec(imagesContent)) !== null) {
          let url = fixIncompleteImageUrl(match[1]);
          
          if (url && url.startsWith('http') && !imageUrls.includes(url)) {
            imageUrls.push(url);
            urlCount++;
          }
        }


      }
    }

    if (imageUrls.length === 0) {
      throw new Error('无法提取图片URL');
    }

    // 去重并修复URL
    imageUrls = [...new Set(imageUrls.map(url => fixIncompleteImageUrl(url)))]
      .filter(url => url && url.startsWith('http'));
    
    return {
      title,
      author,
      videoUrl: imageUrls[0], // 主图片URL
      imageUrls: imageUrls,   // 所有图片
      backgroundVideoUrl: null, // 背景视频URL (通常为空)
      isImageContent: true,
      duration: 0
    };

  } catch (error) {
    console.error('提取图文信息失败:', error);
    throw error;
  }
}

// 修复URL格式（简化版）
function fixIncompleteImageUrl(url) {
  if (!url) return url;
  
  // 处理转义字符
  url = url.replace(/\\u002F/g, '/').replace(/\\\//g, '/');
  
  // 完整URL直接返回
  if (url.startsWith('http')) return url;
  
  // 补全协议
  if (url.startsWith('//')) return 'https:' + url;
  
  // 相对路径添加域名
  return 'https://p3-sign.douyinpic.com/' + url;
}

async function getNoWatermarkVideo(originalUrl) {
  try {
    console.log('开始去水印处理:', originalUrl);

    // 核心去水印方法：将 playwm 替换为 play
    if (originalUrl.includes('/playwm/')) {
      const cleanUrl = originalUrl.replace('/playwm/', '/play/');
      console.log('去水印成功:', cleanUrl);
      return cleanUrl;
    }

    // 如果没有 playwm，直接返回原URL
    console.log('URL中没有水印标识，返回原URL');
    return originalUrl;

  } catch (error) {
    console.error('去水印处理失败:', error);
    return originalUrl;
  }
}

