'use strict';

/**
 * 最右视频解析器 - 精简版
 * 只保留JSON解析，删除HTML解析
 */

exports.main = async (event, context) => {
  const { link, forceRemoveWatermark = false, debug = true } = event;

  if (!link) {
    return {
      success: false,
      message: '链接不能为空'
    };
  }

  try {
    // 检测是否为最右链接
    if (!isZuiyouLink(link)) {
      return {
        success: false,
        message: '仅支持最右链接'
      };
    }

    // 解析最右内容
    const result = await parseZuiyouContent(link, debug);

    return {
      success: true,
      data: result,
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error('最右解析失败:', error);
    return {
      success: false,
      message: error.message || '解析失败',
      error: undefined
    };
  }
};

// 检测是否为最右链接
function isZuiyouLink(link) {
  return /share\.xiaochuankeji\.cn|zuiyou\.com|izuiyou\.com/i.test(link);
}

// 解析最右内容
async function parseZuiyouContent(link, debug = true) {
  if (debug) console.log('开始解析最右链接:', link);
  
  // 提取URL参数
  const urlParams = extractUrlParams(link);
  if (debug) console.log('提取的URL参数: pid=' + urlParams.pid + (urlParams.vid ? ', vid=' + urlParams.vid : ''));
  
  if (!urlParams.pid) {
    throw new Error('无法从链接中提取帖子ID');
  }
  
  // 获取JSON数据
  const jsonData = await fetchJsonData(urlParams, debug);
  
  // 解析JSON数据
  const result = parseJsonData(jsonData, debug);
  
  // 设置原始链接
  result.originalUrl = link;
  
  return result;
}

// 提取URL参数
function extractUrlParams(url) {
  try {
    const urlObj = new URL(url);
    const params = {};
    
    for (const [key, value] of urlObj.searchParams) {
      params[key] = value;
    }
    
    return params;
  } catch (error) {
    console.error('URL参数提取失败:', error);
    return {};
  }
}

// 获取JSON数据
async function fetchJsonData(urlParams, debug = true) {
  const { pid, vid } = urlParams;
  const originalUrl = `https://share.xiaochuankeji.cn/hybrid/share/post?pid=${pid}&vid=${vid || ''}`;
  
  const headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache'
  };
  
  try {
    if (debug) console.log('获取页面HTML:', originalUrl);
    
    const response = await uniCloud.httpclient.request(originalUrl, {
      method: 'GET',
      timeout: 15000,
      dataType: 'text',
      followRedirect: true,
      headers
    });
    
    if (response.status === 200 && response.data) {
      if (debug) console.log('获取HTML成功，页面大小:', response.data.length);
      
      // 从HTML中提取JSON数据
      return extractJsonFromHtml(response.data, debug);
    }
    
    throw new Error('获取页面失败');
  } catch (error) {
    console.error('获取数据失败:', error);
    throw error;
  }
}

// 从HTML中提取JSON数据
function extractJsonFromHtml(html, debug = true) {
  try {
    if (debug) console.log('开始从HTML中提取JSON数据');
    


    // 精准匹配最右的JSON数据
    const pattern = /window\.APP_INITIAL_STATE\s*=\s*({[\s\S]*?})\s*<\/script>/s;
    const match = html.match(pattern);

    if (match) {
      const jsonData = JSON.parse(match[1]);
      if (debug) {
        console.log('✅ 成功提取JSON数据');
      }
      return jsonData;
    }
    
    throw new Error('未找到JSON数据');
  } catch (error) {
    console.error('JSON提取失败:', error);
    throw new Error('JSON数据提取失败: ' + error.message);
  }
}

// 解析JSON数据
function parseJsonData(jsonData, debug = true) {
  try {
    if (debug) console.log('开始解析JSON数据');

    // 查找主帖数据 - 最右的主帖数据在 sharePost.postDetail.post 中
    const post = jsonData.sharePost?.postDetail?.post;

    if (!post) {
      throw new Error('未找到主帖数据');
    }

    // 提取基本信息
    const title = post.content || ''; // 主帖内容作为标题
    const author = post.member?.name || '';

    // 提取媒体信息
    let videoUrls = [];
    let imageUrls = [];
    let coverUrl = '';

    // 检查主帖的视频
    if (post.videos) {
      for (const videoId in post.videos) {
        const video = post.videos[videoId];
        if (video.url) {
          videoUrls.push(convertToHttps(video.url));
        }
      }
    }

    // 检查主帖的图片
    if (post.imgs && Array.isArray(post.imgs)) {
      for (const img of post.imgs) {
        // 获取图片URL
        const imageUrl = selectBestImageUrl(img.urls);
        if (imageUrl) {
          // 构建Live Photo数据 - 所有图片都处理，不管是否为视频帧
          const videoUrl = post.videos && post.videos[img.id] ? post.videos[img.id].url : null;

          const imageData = {
            imageUrl: imageUrl,
            videoUrl: videoUrl ? convertToHttps(videoUrl) : null,
            id: img.id,
            isVideoFrame: img.video === 1 // 标记是否为视频帧
          };
          imageUrls.push(imageData);

          // 第一张图片作为封面
          if (!coverUrl) {
            coverUrl = imageUrl;
          }
        }
      }
    }

    // 设置封面
    if (imageUrls.length > 0) {
      coverUrl = imageUrls[0].imageUrl;
    }

    // 确定内容类型 - 区分纯视频和混合内容
    let contentType = 'unknown';

    // 统计真实图片数量（非视频帧的图片）
    const realImageCount = imageUrls.filter(img => !img.isVideoFrame).length;

    if (videoUrls.length === 1 && realImageCount === 0) {
      // 只有1个视频，没有真实图片 = 纯视频
      contentType = 'video';
      // 纯视频不需要图片数组，只保留封面
      imageUrls = [];
    } else if (imageUrls.length > 0) {
      // 有图片（包括视频帧） = 图集/Live Photo
      contentType = 'image';
    }

    // 构建结果
    return buildResult(title, author, videoUrls, imageUrls, coverUrl, contentType);

  } catch (error) {
    console.error('JSON解析失败:', error);
    throw error;
  }
}

// 选择最佳图片URL - 统一优先选择最高清晰度
function selectBestImageUrl(urls) {
  if (!urls) return null;

  // 统一的清晰度优先级：原图 > WebP原图 > HEIC原图 > 540px > 360px
  // 如果某个链接没有origin，会自动fallback到540px等
  const priorities = [
    'origin',
    'originWebp',
    'originHeic',
    '540',
    '360'
  ];

  for (const priority of priorities) {
    if (urls[priority] && urls[priority].urls && urls[priority].urls[0]) {
      const url = urls[priority].urls[0];
      return convertToHttps(url);
    }
  }

  return null;
}

// 将HTTP链接转换为HTTPS
function convertToHttps(url) {
  if (!url || typeof url !== 'string') return url;

  // 将最右的HTTP链接转换为HTTPS
  if (url.startsWith('http://') && url.includes('izuiyou.com')) {
    return url.replace('http://', 'https://');
  }

  return url;
}

// 构建标准结果
function buildResult(title, author, videoUrls, imageUrls, coverUrl, contentType) {
  if (contentType === 'video') {
    return {
      title: title,
      author: author,
      type: 'video',
      platform: 'zuiyou',
      source: '最右',
      coverUrl: coverUrl,
      processedData: {
        data: videoUrls[0] || '',
        isUrl: true,
        type: 'video/mp4',
        videoUrls: videoUrls,
        imageUrls: [],
        duration: 0
      },
      note: '最右视频解析成功'
    };
  } else if (contentType === 'image') {
    // 检查是否有Live Photo - 必须有真实的视频URL，不能是null
    const hasLivePhoto = imageUrls.some(img => img.videoUrl && img.videoUrl.trim() !== '');

    // 提取纯图片URL数组
    const pureImageUrls = imageUrls.map(img => img.imageUrl);

    // 提取Live Photo视频数组（与图片一一对应）
    const livePhotoVideos = imageUrls.map(img => img.videoUrl);

    // 计算实际Live Photo数量（参考小红书实现）
    const actualLivePhotoCount = livePhotoVideos.filter(video => video && video !== null).length;

    // 提取有效的Live Photo视频URL数组（参考B站实现）
    const validLivePhotoUrls = livePhotoVideos.filter(video => video && video !== null);

    return {
      title: title,
      author: author,
      type: 'image',
      platform: 'zuiyou',
      source: '最右',
      coverUrl: coverUrl,
      processedData: {
        data: pureImageUrls[0] || '',
        isUrl: true,
        type: hasLivePhoto ? 'live_photo' : 'image/jpeg',
        imageUrls: pureImageUrls,
        videoUrls: validLivePhotoUrls, // 只包含有效的Live Photo视频URL，与B站保持一致
        livePhotoVideos: livePhotoVideos, // 与图片一一对应，null表示该图片没有Live Photo
        hasLivePhoto: hasLivePhoto,
        isImageContent: true,
        count: imageUrls.length
      },
      note: hasLivePhoto ?
        `最右Live Photo解析成功（共${imageUrls.length}张图片，${actualLivePhotoCount}个Live Photo）` :
        `最右图片解析成功（共${imageUrls.length}张图片）`
    };
  } else {
    throw new Error('未找到可解析的媒体内容');
  }
}
