# SpringBoot后端快速开始指南

## 项目结构

```
clearmark-backend/
├── src/main/java/com/clearmark/
│   ├── ClearmarkApplication.java          # 主启动类
│   ├── config/
│   │   ├── CorsConfig.java               # 跨域配置
│   │   └── SecurityConfig.java           # 安全配置
│   ├── controller/
│   │   ├── AuthController.java           # 认证控制器
│   │   └── ParserController.java         # 解析控制器
│   ├── service/
│   │   ├── AuthService.java              # 认证服务
│   │   └── ParserService.java            # 解析服务
│   ├── model/
│   │   ├── ApiResponse.java              # 统一响应格式
│   │   └── VideoData.java                # 视频数据模型
│   └── util/
│       ├── ApiKeyUtil.java               # API Key工具
│       └── PlatformDetector.java         # 平台检测工具
├── src/main/resources/
│   └── application.yml                   # 配置文件
└── pom.xml                              # Maven依赖
```

## 核心依赖 (pom.xml)

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.clearmark</groupId>
    <artifactId>clearmark-backend</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.0</version>
        <relativePath/>
    </parent>
    
    <dependencies>
        <!-- Spring Boot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        
        <!-- JSON处理 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        
        <!-- HTTP客户端 -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        
        <!-- 工具类 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        
        <!-- 日志 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
```

## 配置文件 (application.yml)

```yaml
server:
  port: 8080
  servlet:
    context-path: /api/v1

spring:
  application:
    name: clearmark-backend
  
  # 跨域配置
  web:
    cors:
      allowed-origins: "*"
      allowed-methods: "*"
      allowed-headers: "*"

# 自定义配置
clearmark:
  # 主密钥
  master-key: ClearMark-Master-2025-SecureKey-9527
  
  # API Key配置
  api-key:
    length: 32
    expire-hours: 24
  
  # 解析器配置
  parser:
    timeout: 30000
    retry-count: 3
    
  # 支持的平台
  platforms:
    - douyin
    - kuaishou
    - xiaohongshu
    - bilibili
    - weibo
    - weishi
    - pipix
    - qishui
    - zuiyou
    - haokan

# 日志配置
logging:
  level:
    com.clearmark: DEBUG
    org.springframework: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

## 主启动类

```java
package com.clearmark;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class ClearmarkApplication {
    public static void main(String[] args) {
        SpringApplication.run(ClearmarkApplication.class, args);
        System.out.println("🚀 ClearMark后端服务启动成功！");
        System.out.println("📍 服务地址: http://localhost:8080/api/v1");
        System.out.println("📋 API文档: 参考UniApp对接文档.md");
    }
}
```

## 统一响应格式

```java
package com.clearmark.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {
    private boolean success;
    private String message;
    private T data;
    private Long timestamp;
    private Map<String, Object> error;
    
    public ApiResponse() {
        this.timestamp = System.currentTimeMillis();
    }
    
    public static <T> ApiResponse<T> success(T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setSuccess(true);
        response.setMessage("操作成功");
        response.setData(data);
        return response;
    }
    
    public static <T> ApiResponse<T> success(String message, T data) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setSuccess(true);
        response.setMessage(message);
        response.setData(data);
        return response;
    }
    
    public static <T> ApiResponse<T> error(String message) {
        ApiResponse<T> response = new ApiResponse<>();
        response.setSuccess(false);
        response.setMessage(message);
        return response;
    }
    
    // Getters and Setters
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
    
    public T getData() { return data; }
    public void setData(T data) { this.data = data; }
    
    public Long getTimestamp() { return timestamp; }
    public void setTimestamp(Long timestamp) { this.timestamp = timestamp; }
    
    public Map<String, Object> getError() { return error; }
    public void setError(Map<String, Object> error) { this.error = error; }
}
```

## 认证控制器

```java
package com.clearmark.controller;

import com.clearmark.model.ApiResponse;
import com.clearmark.service.AuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/auth")
public class AuthController {
    
    @Autowired
    private AuthService authService;
    
    /**
     * 生成API Key
     */
    @PostMapping("/generate-key")
    public ApiResponse<Map<String, String>> generateApiKey(
            @RequestHeader("X-Master-Key") String masterKey) {
        
        try {
            String apiKey = authService.generateApiKey(masterKey);
            
            Map<String, String> result = new HashMap<>();
            result.put("apiKey", apiKey);
            result.put("usage", "请在请求头中添加 X-API-Key: " + apiKey);
            
            return ApiResponse.success("API Key生成成功", result);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }
}
```

## 解析控制器

```java
package com.clearmark.controller;

import com.clearmark.model.ApiResponse;
import com.clearmark.service.ParserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/parser")
public class ParserController {
    
    @Autowired
    private ParserService parserService;
    
    /**
     * 解析视频
     */
    @PostMapping("/parse")
    public ApiResponse<Map<String, Object>> parseVideo(
            @RequestHeader("X-API-Key") String apiKey,
            @RequestBody Map<String, String> request) {
        
        try {
            String url = request.get("url");
            if (url == null || url.trim().isEmpty()) {
                return ApiResponse.error("URL参数不能为空");
            }
            
            // 验证API Key
            if (!parserService.validateApiKey(apiKey)) {
                return ApiResponse.error("无效的API Key");
            }
            
            // 解析视频
            Map<String, Object> result = parserService.parseVideo(url);
            return ApiResponse.success("解析成功", result);
            
        } catch (Exception e) {
            return ApiResponse.error("解析失败: " + e.getMessage());
        }
    }
    
    /**
     * 检测平台类型
     */
    @GetMapping("/detect")
    public String detectPlatform(
            @RequestHeader("X-API-Key") String apiKey,
            @RequestParam("url") String url) {
        
        // 验证API Key
        if (!parserService.validateApiKey(apiKey)) {
            throw new RuntimeException("无效的API Key");
        }
        
        return parserService.detectPlatform(url);
    }
    
    /**
     * 获取支持的平台
     */
    @GetMapping("/platforms")
    public List<String> getSupportedPlatforms() {
        return parserService.getSupportedPlatforms();
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public String healthCheck() {
        return "OK";
    }
}
```

## 快速启动步骤

1. **创建SpringBoot项目**
   ```bash
   mkdir clearmark-backend
   cd clearmark-backend
   # 复制上述文件到对应目录
   ```

2. **启动服务**
   ```bash
   mvn spring-boot:run
   ```

3. **测试API**
   ```bash
   # 生成API Key
   curl -X POST http://localhost:8080/api/v1/auth/generate-key \
     -H "X-Master-Key: ClearMark-Master-2025-SecureKey-9527"
   
   # 解析视频
   curl -X POST http://localhost:8080/api/v1/parser/parse \
     -H "Content-Type: application/json" \
     -H "X-API-Key: your-api-key" \
     -d '{"url":"https://b23.tv/xxx"}'
   ```

4. **前端配置**
   - 在UniApp中切换到SpringBoot后端
   - 确认服务地址为 `http://localhost:8080`
   - 系统会自动生成和管理API Key

## 注意事项

- 确保端口8080未被占用
- 开发环境可以关闭CORS限制
- 生产环境需要配置HTTPS和域名
- API Key有效期为24小时，过期后自动刷新
- 所有API都需要认证，除了健康检查和平台列表

## 下一步

1. 实现具体的视频解析逻辑
2. 集成各平台的解析器
3. 添加缓存和限流机制
4. 部署到云服务器
5. 配置域名和SSL证书
